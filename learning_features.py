"""
Learning Features Module
=======================

This module implements advanced learning features including interactive tutorials,
progress tracking, adaptive learning paths, and comprehensive analytics.

Repository: https://github.com/asadullah48/tool.git
Author: asadullah48
License: MIT

Features:
- Interactive tutorials with step-by-step guidance
- Adaptive learning paths based on user progress
- Comprehensive learning analytics and insights
- Skill assessment and recommendations
- Learning goals and milestone tracking
"""

import json
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

from config import config, logger
from utils import data_manager, Formatter, learning_tracker
from agents import Runner

class TutorialSystem:
    """Manages interactive tutorials and learning paths"""
    
    def __init__(self):
        self.tutorials = self._define_tutorials()
        self.user_progress = self._load_user_progress()
        
    def _define_tutorials(self) -> Dict[str, Dict[str, Any]]:
        """Define all available tutorials"""
        return {
            "basics": {
                "title": "AI Agents Basics",
                "description": "Learn the fundamentals of AI agents and tools",
                "difficulty": "Beginner",
                "estimated_time": "15 minutes",
                "prerequisites": [],
                "steps": [
                    {
                        "title": "Welcome to AI Agents",
                        "content": "AI agents are intelligent assistants that can use tools to help you accomplish tasks.",
                        "action": "introduction",
                        "agent": "teacher",
                        "query": "What are AI agents and how do they work?"
                    },
                    {
                        "title": "Using the Calculator",
                        "content": "Let's start with a simple calculation to see how agents use tools.",
                        "action": "tool_demo",
                        "agent": "helpful",
                        "query": "Calculate 25 * 4 + 10"
                    },
                    {
                        "title": "Getting Weather Information",
                        "content": "Agents can access various information sources like weather data.",
                        "action": "tool_demo",
                        "agent": "helpful",
                        "query": "What's the weather like in Karachi?"
                    },
                    {
                        "title": "Understanding Agent Personalities",
                        "content": "Different agents have different personalities and specialties.",
                        "action": "comparison",
                        "agent": "teacher",
                        "query": "Explain the differences between agent personalities"
                    }
                ]
            },
            
            "advanced_tools": {
                "title": "Advanced Tool Usage",
                "description": "Master advanced tools and their applications",
                "difficulty": "Intermediate",
                "estimated_time": "25 minutes",
                "prerequisites": ["basics"],
                "steps": [
                    {
                        "title": "Data Analysis",
                        "content": "Learn to analyze data using the data analyzer tool.",
                        "action": "tool_demo",
                        "agent": "analyst",
                        "query": "Analyze this data: [10, 15, 20, 25, 30, 35, 40]"
                    },
                    {
                        "title": "Text Processing",
                        "content": "Explore text analysis and processing capabilities.",
                        "action": "tool_demo",
                        "agent": "technical",
                        "query": "Analyze this text: 'AI agents are revolutionizing how we interact with technology.'"
                    },
                    {
                        "title": "File Operations",
                        "content": "Learn safe file handling and management.",
                        "action": "tool_demo",
                        "agent": "technical",
                        "query": "List the files in the current directory"
                    },
                    {
                        "title": "Learning Analytics",
                        "content": "Track your progress and learning insights.",
                        "action": "analytics",
                        "agent": "tutor",
                        "query": "Show me my learning analytics"
                    }
                ]
            },
            
            "creative_applications": {
                "title": "Creative Applications",
                "description": "Explore creative uses of AI agents",
                "difficulty": "Intermediate",
                "estimated_time": "20 minutes",
                "prerequisites": ["basics"],
                "steps": [
                    {
                        "title": "Creative Problem Solving",
                        "content": "Use agents for brainstorming and creative thinking.",
                        "action": "creative_exercise",
                        "agent": "creative",
                        "query": "Help me brainstorm creative uses for a calculator tool"
                    },
                    {
                        "title": "Research and Analysis",
                        "content": "Combine multiple tools for comprehensive research.",
                        "action": "research_demo",
                        "agent": "researcher",
                        "query": "Research Python programming and provide a comprehensive overview"
                    },
                    {
                        "title": "Educational Content Creation",
                        "content": "Create educational materials using agent capabilities.",
                        "action": "content_creation",
                        "agent": "teacher",
                        "query": "Create a lesson plan about data analysis for beginners"
                    }
                ]
            },
            
            "expert_techniques": {
                "title": "Expert Techniques",
                "description": "Advanced techniques for power users",
                "difficulty": "Advanced",
                "estimated_time": "30 minutes",
                "prerequisites": ["basics", "advanced_tools"],
                "steps": [
                    {
                        "title": "Multi-Agent Workflows",
                        "content": "Learn to coordinate multiple agents for complex tasks.",
                        "action": "workflow_demo",
                        "agent": "technical",
                        "query": "Design a workflow using multiple agents for data analysis"
                    },
                    {
                        "title": "Custom Tool Integration",
                        "content": "Understand how to integrate custom tools effectively.",
                        "action": "integration_demo",
                        "agent": "technical",
                        "query": "Explain how to create and integrate custom tools"
                    },
                    {
                        "title": "Performance Optimization",
                        "content": "Optimize agent performance and efficiency.",
                        "action": "optimization",
                        "agent": "technical",
                        "query": "Show system performance metrics and optimization tips"
                    }
                ]
            }
        }
    
    def _load_user_progress(self) -> Dict[str, Any]:
        """Load user progress from storage"""
        progress = data_manager.load_json("user_progress")
        if progress is None:
            progress = {
                "completed_tutorials": [],
                "current_tutorial": None,
                "current_step": 0,
                "skill_levels": {},
                "achievements": [],
                "total_time_spent": 0
            }
        return progress
    
    def _save_user_progress(self):
        """Save user progress to storage"""
        data_manager.save_json("user_progress", self.user_progress)
    
    def get_available_tutorials(self) -> str:
        """Get list of available tutorials with progress info"""
        result = "📚 Available Tutorials:\n"
        result += "=" * 40 + "\n\n"
        
        for tutorial_id, tutorial in self.tutorials.items():
            # Check if prerequisites are met
            prereqs_met = all(
                prereq in self.user_progress["completed_tutorials"] 
                for prereq in tutorial["prerequisites"]
            )
            
            # Check completion status
            completed = tutorial_id in self.user_progress["completed_tutorials"]
            
            status = "✅ Completed" if completed else ("🔓 Available" if prereqs_met else "🔒 Locked")
            
            result += f"🎓 {tutorial['title']} ({tutorial_id})\n"
            result += f"   📝 {tutorial['description']}\n"
            result += f"   📊 Difficulty: {tutorial['difficulty']}\n"
            result += f"   ⏱️ Time: {tutorial['estimated_time']}\n"
            result += f"   🎯 Status: {status}\n"
            
            if tutorial["prerequisites"]:
                result += f"   📋 Prerequisites: {', '.join(tutorial['prerequisites'])}\n"
            
            result += "\n"
        
        return result
    
    async def start_tutorial(self, tutorial_id: str, agent_manager) -> str:
        """Start a tutorial session"""
        if tutorial_id not in self.tutorials:
            return f"❌ Tutorial '{tutorial_id}' not found"
        
        tutorial = self.tutorials[tutorial_id]
        
        # Check prerequisites
        prereqs_met = all(
            prereq in self.user_progress["completed_tutorials"] 
            for prereq in tutorial["prerequisites"]
        )
        
        if not prereqs_met:
            missing = [p for p in tutorial["prerequisites"] if p not in self.user_progress["completed_tutorials"]]
            return f"❌ Prerequisites not met. Complete these tutorials first: {', '.join(missing)}"
        
        # Start tutorial
        self.user_progress["current_tutorial"] = tutorial_id
        self.user_progress["current_step"] = 0
        self._save_user_progress()
        
        result = f"🎓 Starting Tutorial: {tutorial['title']}\n"
        result += "=" * 50 + "\n"
        result += f"📝 Description: {tutorial['description']}\n"
        result += f"📊 Difficulty: {tutorial['difficulty']}\n"
        result += f"⏱️ Estimated Time: {tutorial['estimated_time']}\n"
        result += f"📋 Steps: {len(tutorial['steps'])}\n\n"
        result += "🚀 Tutorial started! Use 'next_step' to proceed through the tutorial.\n"
        
        return result
    
    async def next_step(self, agent_manager) -> str:
        """Proceed to the next tutorial step"""
        current_tutorial = self.user_progress.get("current_tutorial")
        if not current_tutorial:
            return "❌ No active tutorial. Start a tutorial first."
        
        tutorial = self.tutorials[current_tutorial]
        current_step = self.user_progress["current_step"]
        
        if current_step >= len(tutorial["steps"]):
            return "🎉 Tutorial completed! Use 'complete_tutorial' to finish."
        
        step = tutorial["steps"][current_step]
        
        result = f"📚 Step {current_step + 1}/{len(tutorial['steps'])}: {step['title']}\n"
        result += "=" * 50 + "\n"
        result += f"💡 {step['content']}\n\n"
        
        # Execute step action
        if step["action"] in ["tool_demo", "introduction", "comparison", "analytics"]:
            agent = agent_manager.get_agent(step["agent"])
            try:
                response = await Runner.run(agent, step["query"])
                result += f"🤖 {agent.name} Response:\n{response.final_output}\n\n"
            except Exception as e:
                result += f"❌ Error executing step: {e}\n\n"
        
        result += "⏭️ Type 'next_step' to continue or 'tutorial_help' for options.\n"
        
        # Update progress
        self.user_progress["current_step"] += 1
        self._save_user_progress()
        
        return result
    
    def complete_tutorial(self) -> str:
        """Complete the current tutorial"""
        current_tutorial = self.user_progress.get("current_tutorial")
        if not current_tutorial:
            return "❌ No active tutorial to complete"
        
        # Mark as completed
        if current_tutorial not in self.user_progress["completed_tutorials"]:
            self.user_progress["completed_tutorials"].append(current_tutorial)
        
        # Add achievement
        achievement = f"Completed '{self.tutorials[current_tutorial]['title']}' tutorial"
        if achievement not in self.user_progress["achievements"]:
            self.user_progress["achievements"].append(achievement)
        
        # Reset current tutorial
        self.user_progress["current_tutorial"] = None
        self.user_progress["current_step"] = 0
        
        self._save_user_progress()
        
        result = f"🎉 Tutorial Completed: {self.tutorials[current_tutorial]['title']}\n"
        result += "=" * 50 + "\n"
        result += f"✅ Achievement Unlocked: {achievement}\n"
        result += f"📊 Total Completed: {len(self.user_progress['completed_tutorials'])}\n\n"
        
        # Suggest next tutorial
        available = self.get_next_recommendations()
        if available:
            result += f"🎯 Recommended Next: {available}\n"
        
        return result
    
    def get_next_recommendations(self) -> str:
        """Get recommendations for next tutorials"""
        completed = set(self.user_progress["completed_tutorials"])
        
        recommendations = []
        for tutorial_id, tutorial in self.tutorials.items():
            if tutorial_id not in completed:
                prereqs_met = all(prereq in completed for prereq in tutorial["prerequisites"])
                if prereqs_met:
                    recommendations.append(f"{tutorial['title']} ({tutorial_id})")
        
        return ", ".join(recommendations[:3]) if recommendations else "All tutorials completed! 🎉"

class SkillAssessment:
    """Assesses user skills and provides recommendations"""
    
    def __init__(self):
        self.skill_areas = {
            "basic_operations": ["calculator", "system_info"],
            "data_analysis": ["data_analyzer", "advanced_calculator"],
            "text_processing": ["text_processor", "web_search"],
            "file_management": ["file_operations"],
            "research_skills": ["web_search", "learning_analytics"],
            "creative_thinking": ["text_processor", "web_search"]
        }
    
    def assess_skills(self, tool_usage_stats: Dict) -> Dict[str, Any]:
        """Assess user skills based on tool usage"""
        assessment = {}
        
        for skill_area, tools in self.skill_areas.items():
            total_usage = sum(tool_usage_stats.get(tool, {}).get("count", 0) for tool in tools)
            success_rate = self._calculate_success_rate(tools, tool_usage_stats)
            
            # Determine skill level
            if total_usage == 0:
                level = "Beginner"
                score = 0
            elif total_usage < 5:
                level = "Novice"
                score = 25
            elif total_usage < 15:
                level = "Intermediate"
                score = 50 + (success_rate * 0.3)
            elif total_usage < 30:
                level = "Advanced"
                score = 75 + (success_rate * 0.25)
            else:
                level = "Expert"
                score = 90 + (success_rate * 0.1)
            
            assessment[skill_area] = {
                "level": level,
                "score": min(100, score),
                "usage_count": total_usage,
                "success_rate": success_rate,
                "tools": tools
            }
        
        return assessment
    
    def _calculate_success_rate(self, tools: List[str], tool_usage_stats: Dict) -> float:
        """Calculate success rate for tools"""
        total_uses = 0
        total_errors = 0
        
        for tool in tools:
            if tool in tool_usage_stats:
                stats = tool_usage_stats[tool]
                total_uses += stats.get("count", 0)
                total_errors += stats.get("errors", 0)
        
        if total_uses == 0:
            return 100.0
        
        return ((total_uses - total_errors) / total_uses) * 100
    
    def generate_skill_report(self, tool_usage_stats: Dict) -> str:
        """Generate comprehensive skill assessment report"""
        assessment = self.assess_skills(tool_usage_stats)
        
        report = "🎯 Skill Assessment Report:\n"
        report += "=" * 40 + "\n\n"
        
        # Overall score
        overall_score = sum(skill["score"] for skill in assessment.values()) / len(assessment)
        report += f"📊 Overall Skill Level: {overall_score:.1f}/100\n\n"
        
        # Individual skills
        for skill_area, data in assessment.items():
            report += f"🔧 {skill_area.replace('_', ' ').title()}:\n"
            report += f"   📈 Level: {data['level']}\n"
            report += f"   📊 Score: {data['score']:.1f}/100\n"
            report += f"   🔢 Usage: {data['usage_count']} times\n"
            report += f"   ✅ Success Rate: {data['success_rate']:.1f}%\n"
            report += f"   🛠️ Tools: {', '.join(data['tools'])}\n\n"
        
        # Recommendations
        report += self._generate_recommendations(assessment)
        
        return report
    
    def _generate_recommendations(self, assessment: Dict) -> str:
        """Generate personalized recommendations"""
        recommendations = "💡 Personalized Recommendations:\n"
        recommendations += "-" * 35 + "\n"
        
        # Find areas for improvement
        weak_areas = [area for area, data in assessment.items() if data["score"] < 50]
        strong_areas = [area for area, data in assessment.items() if data["score"] > 75]
        
        if weak_areas:
            recommendations += f"🎯 Focus Areas: {', '.join(weak_areas)}\n"
            recommendations += "   Practice these tools more to improve your skills.\n\n"
        
        if strong_areas:
            recommendations += f"💪 Strong Areas: {', '.join(strong_areas)}\n"
            recommendations += "   Consider helping others or exploring advanced features.\n\n"
        
        recommendations += "📚 Suggested Actions:\n"
        recommendations += "   • Complete relevant tutorials\n"
        recommendations += "   • Practice with different tool combinations\n"
        recommendations += "   • Explore advanced features\n"
        recommendations += "   • Share knowledge with others\n"
        
        return recommendations

# Create global instances
tutorial_system = TutorialSystem()
skill_assessment = SkillAssessment()

# Export for easy import
__all__ = ['TutorialSystem', 'SkillAssessment', 'tutorial_system', 'skill_assessment']
