# 🚀 Quick Start Guide

Get up and running with the Enhanced AI Agents System in just a few minutes!

**Repository:** [https://github.com/asadullah48/tool.git](https://github.com/asadullah48/tool.git)
**Author:** [asad<PERSON><PERSON><PERSON>](https://github.com/asadullah48)
**License:** MIT

**🎓 Learning Source:** [Panaversity - Learn Agentic AI](https://panaversity.org/)
**📚 Course Materials:** [AI Agents First](https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first)

## ⚡ 5-Minute Setup

### 0. **Clone the Repository** (1 minute)
```bash
git clone https://github.com/asadullah48/tool.git
cd tool
```

### 1. **Get Your API Key** (2 minutes)
1. Go to [Google AI Studio](https://ai.google.dev/)
2. Sign in with your Google account
3. Create a new API key
4. Copy the API key

### 2. **Configure the Project** (1 minute)
1. Open the `.env` file in the project directory
2. Add your API key:
   ```env
   GEMINI_API_KEY=your_api_key_here
   ```
3. Save the file

### 3. **Install Dependencies** (1 minute)
```bash
# Using UV (recommended)
uv add streamlit

# Or using pip
pip install -r requirements.txt
```

### 4. **Run Your First Command** (1 minute)
```bash
python main.py --query "What's the weather in Karachi?"
```

🎉 **That's it!** You should see your first AI agent response.

## 🎯 What to Try Next

### Interactive Chat Mode
```bash
python main.py
```
Then try these commands:
- `Calculate 25 * 4 + 10`
- `Find student with roll number 1`
- `What's the weather in Lahore?`
- `/help` for more commands

### Tutorial Mode (Recommended for Beginners)
```bash
python main.py --tutorial
```

### Demo Mode (See All Features)
```bash
python main.py --demo
```

### Different Agent Personalities
```bash
python main.py --agent teacher --query "Explain AI concepts"
python main.py --agent technical --query "How does the calculator work?"
python main.py --agent friendly --query "Tell me about data analysis"
```

## 🛠️ Available Tools

Try these example queries to explore different tools:

### 🧮 **Calculator Tool**
- `Calculate sqrt(144) + 25 * 2`
- `What's 15% of 200?`
- `Calculate sin(30) * pi`

### 👨‍🎓 **Student Database**
- `Find student with roll number 2`
- `Show me information about student 5`
- `List all available students`

### 🌤️ **Weather Information**
- `What's the weather in Islamabad?`
- `Show weather for Karachi and Lahore`
- `Get weather information for Peshawar`

### 📊 **Data Analysis**
- `Analyze this data: [10, 15, 20, 25, 30]`
- `Calculate statistics for [85, 90, 78, 92, 88]`
- `Find the mean and median of [1,2,3,4,5,6,7,8,9,10]`

### 📝 **Text Processing**
- `Analyze this text: "AI is transforming education"`
- `Count words in: "Hello world from AI agents"`
- `Extract information from: "Contact <NAME_EMAIL>"`

### 🔍 **Web Search**
- `Search for Python programming tutorials`
- `Find information about machine learning`
- `Search for data science resources`

### 📁 **File Operations**
- `List files in current directory`
- `Show information about main.py`
- `Search for "config" in files`

### 📈 **Learning Analytics**
- `Show my learning progress`
- `Display tool usage statistics`
- `Generate skill assessment report`

### 💻 **System Information**
- `Show system information`
- `Display Python environment details`
- `Show application configuration`

## 🤖 Agent Personalities Guide

### 🆘 **Helpful Assistant** (`helpful`)
- **Best for**: General questions and everyday tasks
- **Example**: `python main.py --agent helpful --query "Help me understand AI"`

### 👩‍🏫 **AI Teacher** (`teacher`)
- **Best for**: Learning new concepts and educational content
- **Example**: `python main.py --agent teacher --query "Teach me about statistics"`

### 🔧 **Technical Expert** (`technical`)
- **Best for**: Technical explanations and detailed analysis
- **Example**: `python main.py --agent technical --query "Explain how algorithms work"`

### 😊 **Friendly Companion** (`friendly`)
- **Best for**: Casual conversations and encouragement
- **Example**: `python main.py --agent friendly --query "Motivate me to learn coding"`

### 🔬 **Research Assistant** (`researcher`)
- **Best for**: In-depth research and comprehensive analysis
- **Example**: `python main.py --agent researcher --query "Research AI trends"`

### 🎯 **Personal Tutor** (`tutor`)
- **Best for**: Personalized learning and progress tracking
- **Example**: `python main.py --agent tutor --query "Help me improve my skills"`

### 🎨 **Creative Collaborator** (`creative`)
- **Best for**: Brainstorming and creative problem-solving
- **Example**: `python main.py --agent creative --query "Creative uses for AI"`

### 📊 **Data Analyst** (`analyst`)
- **Best for**: Data analysis and statistical insights
- **Example**: `python main.py --agent analyst --query "Analyze sales data"`

## 🎓 Learning Path

### **Beginner** (Start Here!)
1. Run tutorial mode: `python main.py --tutorial`
2. Try basic calculations and weather queries
3. Explore different agent personalities
4. Use the interactive chat mode

### **Intermediate**
1. Try data analysis tools
2. Explore text processing features
3. Use file operations
4. Check your learning analytics

### **Advanced**
1. Run the demo mode to see all features
2. Try complex multi-tool queries
3. Explore the example scripts
4. Customize agent personalities

## 🔧 Troubleshooting

### **"Configuration Error" Message**
- Check that your API key is correctly set in `.env`
- Make sure the `.env` file is in the project root directory

### **"Import Error" Message**
- Install dependencies: `pip install openai-agents python-dotenv requests nest-asyncio`
- Check your Python version (3.8+ required)

### **"API Error" Message**
- Verify your API key is valid
- Check your internet connection
- Ensure you have API quota remaining

### **Slow Responses**
- Check your internet connection
- Try a simpler query first
- Enable debug mode to see detailed logs

## 💡 Pro Tips

1. **Use `/help` in chat mode** to see all available commands
2. **Try different agents** for the same question to see varied responses
3. **Use analytics** to track your learning progress
4. **Start with tutorials** if you're new to AI agents
5. **Combine tools** in single queries for powerful results

## 📚 Next Steps

- Read the full [README.md](README.md) for comprehensive documentation
- Try the [basic examples](examples/basic_usage.py)
- Explore [advanced examples](examples/advanced_examples.py)
- Customize the system for your specific needs

## 🆘 Need Help?

- Use the built-in tutorial: `python main.py --tutorial`
- Check the examples in the `examples/` directory
- Enable debug mode in `.env` for detailed logs
- Review the comprehensive documentation in README.md

---

**Ready to explore AI agents? Start with the tutorial mode and have fun learning! 🚀**
