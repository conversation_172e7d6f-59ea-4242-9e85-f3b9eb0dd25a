"""
Enhanced Configuration Module for AI Agents
===========================================

This module provides comprehensive configuration management for the AI agents project,
including environment variable handling, logging setup, and model configuration.

Repository: https://github.com/asadullah48/tool.git
Author: asadullah48
License: MIT

Features:
- Environment variable validation
- Multiple model provider support
- Logging configuration
- Error handling and validation
- Development vs production settings
"""

import os
import sys
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from dotenv import load_dotenv, find_dotenv
from agents import AsyncOpenAI, OpenAIChatCompletionsModel, set_default_openai_client
from agents.run import RunConfig

class ConfigurationError(Exception):
    """Custom exception for configuration errors"""
    pass

class Config:
    """Enhanced configuration class with validation and error handling"""

    def __init__(self):
        """Initialize configuration with environment variables"""
        self._load_environment()
        self._setup_logging()
        self._validate_required_settings()
        self._setup_clients()

    def _load_environment(self):
        """Load environment variables from .env file"""
        # Try to find .env file
        env_file = find_dotenv()
        if env_file:
            load_dotenv(env_file)
            print(f"✅ Loaded environment from: {env_file}")
        else:
            print("⚠️  No .env file found. Using system environment variables.")

        # Load all configuration values
        self.gemini_api_key = os.getenv("GEMINI_API_KEY")
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.default_model = os.getenv("DEFAULT_MODEL", "gemini-2.0-flash")
        self.environment = os.getenv("ENVIRONMENT", "development")
        self.debug = os.getenv("DEBUG", "true").lower() == "true"
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.max_conversation_history = int(os.getenv("MAX_CONVERSATION_HISTORY", "50"))

        # API URLs
        self.gemini_base_url = os.getenv("GEMINI_BASE_URL", "https://generativelanguage.googleapis.com/v1beta/openai/")
        self.openai_base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")

        # Tool settings
        self.allow_file_operations = os.getenv("ALLOW_FILE_OPERATIONS", "true").lower() == "true"
        self.max_file_size_mb = int(os.getenv("MAX_FILE_SIZE_MB", "10"))
        self.allowed_file_extensions = os.getenv("ALLOWED_FILE_EXTENSIONS", ".txt,.json,.csv,.md,.py").split(",")

        # Learning features
        self.enable_analytics = os.getenv("ENABLE_ANALYTICS", "true").lower() == "true"
        self.enable_tutorial_mode = os.getenv("ENABLE_TUTORIAL_MODE", "true").lower() == "true"
        self.show_tool_explanations = os.getenv("SHOW_TOOL_EXPLANATIONS", "true").lower() == "true"

        # Security settings
        self.allow_code_execution = os.getenv("ALLOW_CODE_EXECUTION", "false").lower() == "true"
        self.allow_system_commands = os.getenv("ALLOW_SYSTEM_COMMANDS", "false").lower() == "true"
        self.max_requests_per_minute = int(os.getenv("MAX_REQUESTS_PER_MINUTE", "60"))

        # Advanced settings
        self.request_timeout = int(os.getenv("REQUEST_TIMEOUT", "30"))
        self.max_retry_attempts = int(os.getenv("MAX_RETRY_ATTEMPTS", "3"))

    def _setup_logging(self):
        """Setup logging configuration"""
        # Create logs directory if it doesn't exist
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        # Configure logging
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

        # Set up file handler
        file_handler = logging.FileHandler(log_dir / "agents.log")
        file_handler.setFormatter(logging.Formatter(log_format))

        # Set up console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(logging.Formatter(log_format))

        # Configure root logger
        logging.basicConfig(
            level=getattr(logging, self.log_level.upper()),
            handlers=[file_handler, console_handler] if self.debug else [file_handler],
            format=log_format
        )

        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Logging configured - Level: {self.log_level}, Environment: {self.environment}")

    def _validate_required_settings(self):
        """Validate required configuration settings"""
        errors = []
        warnings = []

        # Check for required API keys (allow demo mode)
        if not self.gemini_api_key and not self.openai_api_key:
            if self.environment == "production":
                errors.append("At least one API key (GEMINI_API_KEY or OPENAI_API_KEY) must be provided")
            else:
                warnings.append("No API keys found - running in demo mode")

        # Validate model selection (but allow demo mode)
        if self.default_model.startswith("gemini") and not self.gemini_api_key:
            if self.environment == "production":
                errors.append("GEMINI_API_KEY is required when using Gemini models")
            else:
                warnings.append("GEMINI_API_KEY not found - AI agent features will be limited")

        if self.default_model.startswith("gpt") and not self.openai_api_key:
            if self.environment == "production":
                errors.append("OPENAI_API_KEY is required when using OpenAI models")
            else:
                warnings.append("OPENAI_API_KEY not found - AI agent features will be limited")

        # Validate numeric settings
        if self.max_conversation_history < 1:
            errors.append("MAX_CONVERSATION_HISTORY must be at least 1")

        if self.max_file_size_mb < 1:
            errors.append("MAX_FILE_SIZE_MB must be at least 1")

        # Show warnings
        if warnings:
            for warning in warnings:
                print(f"⚠️ Warning: {warning}")

        if errors:
            error_message = "Configuration validation failed:\n" + "\n".join(f"- {error}" for error in errors)
            self.logger.error(error_message)
            raise ConfigurationError(error_message)

        self.logger.info("✅ Configuration validation passed")

    def _setup_clients(self):
        """Setup API clients based on configuration"""
        self.clients = {}
        self.models = {}

        # Setup Gemini client if API key is available
        if self.gemini_api_key:
            try:
                self.clients['gemini'] = AsyncOpenAI(
                    api_key=self.gemini_api_key,
                    base_url=self.gemini_base_url,
                )

                self.models['gemini'] = OpenAIChatCompletionsModel(
                    model="gemini-2.0-flash",
                    openai_client=self.clients['gemini']
                )

                # Set as default client
                set_default_openai_client(self.clients['gemini'])
                self.logger.info("✅ Gemini client configured successfully")

            except Exception as e:
                self.logger.error(f"Failed to setup Gemini client: {e}")
                raise ConfigurationError(f"Gemini client setup failed: {e}")

        # Setup OpenAI client if API key is available
        if self.openai_api_key:
            try:
                self.clients['openai'] = AsyncOpenAI(
                    api_key=self.openai_api_key,
                    base_url=self.openai_base_url,
                )

                self.models['openai'] = OpenAIChatCompletionsModel(
                    model="gpt-4",
                    openai_client=self.clients['openai']
                )

                self.logger.info("✅ OpenAI client configured successfully")

            except Exception as e:
                self.logger.error(f"Failed to setup OpenAI client: {e}")
                # Don't raise error for OpenAI as it's optional

        # Set default model
        if self.default_model.startswith("gemini") and 'gemini' in self.models:
            self.model = self.models['gemini']
            self.client = self.clients['gemini']
        elif self.default_model.startswith("gpt") and 'openai' in self.models:
            self.model = self.models['openai']
            self.client = self.clients['openai']
        else:
            # Fallback to first available model
            if self.models:
                model_name = list(self.models.keys())[0]
                self.model = self.models[model_name]
                self.client = self.clients[model_name]
                self.logger.warning(f"Using fallback model: {model_name}")
            else:
                raise ConfigurationError("No valid models configured")

        # Create run config
        self.run_config = RunConfig(
            model=self.model,
            model_provider=self.client,
            tracing_disabled=not self.debug
        )

        self.logger.info(f"✅ Using model: {self.default_model}")

    def get_model(self, model_name: Optional[str] = None) -> OpenAIChatCompletionsModel:
        """Get a specific model by name"""
        if model_name is None:
            return self.model

        if model_name.startswith("gemini") and 'gemini' in self.models:
            return self.models['gemini']
        elif model_name.startswith("gpt") and 'openai' in self.models:
            return self.models['openai']
        else:
            raise ValueError(f"Model {model_name} not available")

    def get_client(self, provider: Optional[str] = None) -> AsyncOpenAI:
        """Get a specific client by provider name"""
        if provider is None:
            return self.client

        if provider in self.clients:
            return self.clients[provider]
        else:
            raise ValueError(f"Provider {provider} not available")

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary (excluding sensitive data)"""
        return {
            "default_model": self.default_model,
            "environment": self.environment,
            "debug": self.debug,
            "log_level": self.log_level,
            "max_conversation_history": self.max_conversation_history,
            "allow_file_operations": self.allow_file_operations,
            "enable_analytics": self.enable_analytics,
            "enable_tutorial_mode": self.enable_tutorial_mode,
            "available_models": list(self.models.keys()),
            "available_clients": list(self.clients.keys())
        }

# Create global configuration instance
try:
    config = Config()
    print("🚀 Configuration loaded successfully!")

    # Print configuration summary if in debug mode
    if config.debug:
        print("\n📋 Configuration Summary:")
        for key, value in config.to_dict().items():
            print(f"  {key}: {value}")
        print()

except ConfigurationError as e:
    print(f"❌ Configuration Error: {e}")
    print("\n💡 Please check your .env file and ensure all required settings are configured.")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected error during configuration: {e}")
    sys.exit(1)

# Export commonly used objects for easy import
model = config.model
client = config.client
run_config = config.run_config
logger = config.logger