"""
Tools Collection Module
======================

This module contains all the custom tools used by AI agents.
Each tool is designed to be educational and demonstrate different capabilities.

Tools included:
- Calculator: Enhanced mathematical calculations
- Student Finder: Database search functionality
- Weather: Location-based information
- File Operations: Safe file handling
- Web Search: Information retrieval simulation
- Learning Analytics: Progress tracking
- System Info: Environment information
"""

import os
import time
import random
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List
import math

from agents.tool import function_tool
from config import config, logger

# Global tool usage tracking
tool_usage_stats = {}

def track_tool_usage(tool_name: str, execution_time: float = 0):
    """Track tool usage for learning analytics"""
    if not config.enable_analytics:
        return
    
    if tool_name not in tool_usage_stats:
        tool_usage_stats[tool_name] = {
            "count": 0,
            "total_time": 0,
            "average_time": 0,
            "last_used": None,
            "errors": 0
        }
    
    stats = tool_usage_stats[tool_name]
    stats["count"] += 1
    stats["total_time"] += execution_time
    stats["average_time"] = stats["total_time"] / stats["count"]
    stats["last_used"] = datetime.now().isoformat()

def track_tool_error(tool_name: str):
    """Track tool errors for analytics"""
    if tool_name in tool_usage_stats:
        tool_usage_stats[tool_name]["errors"] += 1

def explain_tool(tool_name: str, description: str):
    """Provide educational explanation about tool usage"""
    if config.show_tool_explanations:
        logger.info(f"🔧 Tool Used: {tool_name}")
        logger.info(f"📖 Purpose: {description}")

# =============================================================================
# MATHEMATICAL TOOLS
# =============================================================================

@function_tool("advanced_calculator")
def advanced_calculator(expression: str) -> str:
    """
    Advanced mathematical calculator with support for complex operations.
    
    Supported functions:
    - Basic operations: +, -, *, /, **, %
    - Mathematical functions: sin, cos, tan, log, sqrt, abs
    - Constants: pi, e
    - Advanced: factorial, combinations, permutations
    
    Args:
        expression: Mathematical expression to evaluate
    
    Returns:
        Calculation result with detailed explanation
    """
    start_time = time.time()
    
    try:
        explain_tool("advanced_calculator", "Performs complex mathematical calculations with detailed explanations")
        
        # Enhanced mathematical functions
        def factorial(n):
            if n < 0 or not isinstance(n, int):
                raise ValueError("Factorial requires non-negative integer")
            return math.factorial(int(n))
        
        def combination(n, r):
            return math.factorial(int(n)) // (math.factorial(int(r)) * math.factorial(int(n) - int(r)))
        
        def permutation(n, r):
            return math.factorial(int(n)) // math.factorial(int(n) - int(r))
        
        # Prepare safe expression
        safe_expression = expression.lower().replace(' ', '')
        
        # Replace mathematical functions and constants
        replacements = {
            'sqrt': 'math.sqrt',
            'sin': 'math.sin',
            'cos': 'math.cos',
            'tan': 'math.tan',
            'log': 'math.log',
            'ln': 'math.log',
            'abs': 'abs',
            'pi': 'math.pi',
            'e': 'math.e',
            'factorial': 'factorial',
            'fact': 'factorial',
            'comb': 'combination',
            'perm': 'permutation'
        }
        
        for old, new in replacements.items():
            safe_expression = safe_expression.replace(old, new)
        
        # Create safe namespace
        safe_dict = {
            "__builtins__": {},
            "math": math,
            "factorial": factorial,
            "combination": combination,
            "permutation": permutation
        }
        
        # Validate expression
        allowed_chars = set('0123456789+-*/.()abcdefghijklmnopqrstuvwxyz_,')
        if not all(c in allowed_chars for c in safe_expression):
            return "❌ Error: Expression contains invalid characters"
        
        # Evaluate
        result = eval(safe_expression, safe_dict)
        
        # Format result
        if isinstance(result, float):
            if result.is_integer():
                result = int(result)
            else:
                result = round(result, 8)
        
        execution_time = time.time() - start_time
        track_tool_usage("advanced_calculator", execution_time)
        
        # Provide educational context
        explanation = ""
        if 'sin' in expression or 'cos' in expression or 'tan' in expression:
            explanation += "\n📐 Trigonometric functions work with radians. Use math.radians() for degrees."
        if 'factorial' in expression:
            explanation += "\n🔢 Factorial (n!) is the product of all positive integers up to n."
        if 'sqrt' in expression:
            explanation += "\n√ Square root finds the number that when multiplied by itself gives the original number."
        
        return f"🧮 Advanced Calculation:\n📝 Expression: {expression}\n✅ Result: {result}\n⏱️ Time: {execution_time:.3f}s{explanation}"
        
    except Exception as e:
        track_tool_error("advanced_calculator")
        return f"❌ Calculation Error: {str(e)}\n💡 Tip: Check your syntax and ensure all functions are supported"

# =============================================================================
# DATA MANAGEMENT TOOLS
# =============================================================================

@function_tool("data_analyzer")
def data_analyzer(data: str, operation: str = "summary") -> str:
    """
    Analyze numerical data with statistical operations.
    
    Args:
        data: Comma-separated numbers or JSON array
        operation: Type of analysis (summary, mean, median, mode, range, std)
    
    Returns:
        Statistical analysis results
    """
    start_time = time.time()
    
    try:
        explain_tool("data_analyzer", f"Performing {operation} analysis on numerical data")
        
        # Parse data
        if data.startswith('[') and data.endswith(']'):
            # JSON array format
            numbers = json.loads(data)
        else:
            # Comma-separated format
            numbers = [float(x.strip()) for x in data.split(',')]
        
        if not numbers:
            return "❌ No valid numbers found in data"
        
        # Perform analysis
        if operation == "summary":
            mean_val = sum(numbers) / len(numbers)
            sorted_nums = sorted(numbers)
            n = len(sorted_nums)
            median_val = sorted_nums[n//2] if n % 2 == 1 else (sorted_nums[n//2-1] + sorted_nums[n//2]) / 2
            min_val, max_val = min(numbers), max(numbers)
            range_val = max_val - min_val
            
            # Standard deviation
            variance = sum((x - mean_val) ** 2 for x in numbers) / len(numbers)
            std_dev = variance ** 0.5
            
            execution_time = time.time() - start_time
            track_tool_usage("data_analyzer", execution_time)
            
            return (
                f"📊 Data Analysis Summary:\n"
                f"📈 Dataset: {numbers}\n"
                f"🔢 Count: {len(numbers)}\n"
                f"📊 Mean: {mean_val:.3f}\n"
                f"📍 Median: {median_val:.3f}\n"
                f"⬇️ Min: {min_val}\n"
                f"⬆️ Max: {max_val}\n"
                f"📏 Range: {range_val}\n"
                f"📐 Std Dev: {std_dev:.3f}\n"
                f"⏱️ Analysis Time: {execution_time:.3f}s"
            )
        
        elif operation == "mean":
            result = sum(numbers) / len(numbers)
            return f"📊 Mean (Average): {result:.3f}"
        
        elif operation == "median":
            sorted_nums = sorted(numbers)
            n = len(sorted_nums)
            result = sorted_nums[n//2] if n % 2 == 1 else (sorted_nums[n//2-1] + sorted_nums[n//2]) / 2
            return f"📍 Median (Middle Value): {result:.3f}"
        
        elif operation == "mode":
            from collections import Counter
            counts = Counter(numbers)
            max_count = max(counts.values())
            modes = [num for num, count in counts.items() if count == max_count]
            return f"🎯 Mode (Most Frequent): {modes} (appears {max_count} times)"
        
        elif operation == "range":
            result = max(numbers) - min(numbers)
            return f"📏 Range (Max - Min): {result}"
        
        elif operation == "std":
            mean_val = sum(numbers) / len(numbers)
            variance = sum((x - mean_val) ** 2 for x in numbers) / len(numbers)
            result = variance ** 0.5
            return f"📐 Standard Deviation: {result:.3f}"
        
        else:
            return "❌ Invalid operation. Use: summary, mean, median, mode, range, std"
    
    except Exception as e:
        track_tool_error("data_analyzer")
        return f"❌ Data Analysis Error: {str(e)}"

# =============================================================================
# TEXT PROCESSING TOOLS
# =============================================================================

@function_tool("text_processor")
def text_processor(text: str, operation: str = "analyze") -> str:
    """
    Process and analyze text with various operations.
    
    Args:
        text: Input text to process
        operation: Type of processing (analyze, count, clean, extract, sentiment)
    
    Returns:
        Text processing results
    """
    start_time = time.time()
    
    try:
        explain_tool("text_processor", f"Processing text with {operation} operation")
        
        if operation == "analyze":
            # Basic text analysis
            char_count = len(text)
            word_count = len(text.split())
            sentence_count = text.count('.') + text.count('!') + text.count('?')
            paragraph_count = text.count('\n\n') + 1
            
            # Character frequency
            char_freq = {}
            for char in text.lower():
                if char.isalpha():
                    char_freq[char] = char_freq.get(char, 0) + 1
            
            most_common_char = max(char_freq.items(), key=lambda x: x[1]) if char_freq else ('N/A', 0)
            
            execution_time = time.time() - start_time
            track_tool_usage("text_processor", execution_time)
            
            return (
                f"📝 Text Analysis Results:\n"
                f"🔤 Characters: {char_count}\n"
                f"📝 Words: {word_count}\n"
                f"📄 Sentences: {sentence_count}\n"
                f"📋 Paragraphs: {paragraph_count}\n"
                f"🎯 Most Common Letter: '{most_common_char[0]}' ({most_common_char[1]} times)\n"
                f"📊 Average Words/Sentence: {word_count/max(sentence_count, 1):.1f}\n"
                f"⏱️ Processing Time: {execution_time:.3f}s"
            )
        
        elif operation == "count":
            return f"📊 Text Statistics:\n🔤 Characters: {len(text)}\n📝 Words: {len(text.split())}\n📄 Lines: {text.count(chr(10)) + 1}"
        
        elif operation == "clean":
            import re
            # Remove extra whitespace and special characters
            cleaned = re.sub(r'\s+', ' ', text.strip())
            cleaned = re.sub(r'[^\w\s.,!?-]', '', cleaned)
            return f"🧹 Cleaned Text:\n{cleaned}"
        
        elif operation == "extract":
            import re
            # Extract emails, URLs, and phone numbers
            emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
            urls = re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', text)
            phones = re.findall(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b', text)
            
            return (
                f"🔍 Extracted Information:\n"
                f"📧 Emails: {emails if emails else 'None found'}\n"
                f"🌐 URLs: {urls if urls else 'None found'}\n"
                f"📞 Phone Numbers: {phones if phones else 'None found'}"
            )
        
        elif operation == "sentiment":
            # Simple sentiment analysis based on keywords
            positive_words = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'like', 'happy', 'joy']
            negative_words = ['bad', 'terrible', 'awful', 'hate', 'dislike', 'sad', 'angry', 'disappointed', 'frustrated']
            
            text_lower = text.lower()
            positive_count = sum(1 for word in positive_words if word in text_lower)
            negative_count = sum(1 for word in negative_words if word in text_lower)
            
            if positive_count > negative_count:
                sentiment = "😊 Positive"
            elif negative_count > positive_count:
                sentiment = "😞 Negative"
            else:
                sentiment = "😐 Neutral"
            
            return (
                f"🎭 Sentiment Analysis:\n"
                f"📊 Overall Sentiment: {sentiment}\n"
                f"😊 Positive Indicators: {positive_count}\n"
                f"😞 Negative Indicators: {negative_count}\n"
                f"💡 Note: This is a simple keyword-based analysis"
            )
        
        else:
            return "❌ Invalid operation. Use: analyze, count, clean, extract, sentiment"
    
    except Exception as e:
        track_tool_error("text_processor")
        return f"❌ Text Processing Error: {str(e)}"

# Export all tools for easy import
__all__ = [
    'advanced_calculator',
    'data_analyzer', 
    'text_processor',
    'tool_usage_stats',
    'track_tool_usage',
    'track_tool_error',
    'explain_tool'
]
