"""
Enhanced AI Agents Main Application
===================================

This is the main application file that demonstrates the use of AI agents with various tools.
It includes all the tools from the Jupyter notebook plus additional learning-focused features.

Features:
- Multiple AI agent personalities
- Comprehensive tool suite
- Interactive chat interface
- Learning analytics and tutorials
- Conversation history
- Performance monitoring

Usage:
    python main.py                    # Interactive mode
    python main.py --demo            # Run demo scenarios
    python main.py --tutorial       # Start tutorial mode
    python main.py --query "text"   # Single query mode
"""

import asyncio
import argparse
import json
import random
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import time

# Import configuration and agents
from config import config, logger
from agents import Agent, Runner, function_tool

# Import additional modules for enhanced functionality
import requests
from urllib.parse import quote

# =============================================================================
# ENHANCED TOOLS IMPLEMENTATION
# =============================================================================

# Tool usage tracking for learning analytics
tool_usage_stats = {}

def track_tool_usage(tool_name: str, execution_time: float = 0):
    """Track tool usage for analytics"""
    if not config.enable_analytics:
        return

    if tool_name not in tool_usage_stats:
        tool_usage_stats[tool_name] = {
            "count": 0,
            "total_time": 0,
            "average_time": 0,
            "last_used": None
        }

    stats = tool_usage_stats[tool_name]
    stats["count"] += 1
    stats["total_time"] += execution_time
    stats["average_time"] = stats["total_time"] / stats["count"]
    stats["last_used"] = datetime.now().isoformat()

def explain_tool(tool_name: str, description: str):
    """Provide educational explanation about tool usage"""
    if config.show_tool_explanations:
        logger.info(f"🔧 Tool Used: {tool_name}")
        logger.info(f"📖 Purpose: {description}")

# Enhanced Calculator Tool
@function_tool("calculator")
def calculator(expression: str) -> str:
    """
    Perform mathematical calculations safely with enhanced features.

    Args:
        expression: Mathematical expression to evaluate (e.g., "2 + 3 * 4", "sqrt(16)", "sin(30)")

    Returns:
        Calculation result or error message
    """
    start_time = time.time()

    try:
        explain_tool("calculator", "Performs safe mathematical calculations with support for basic operations")

        # Enhanced allowed characters including mathematical functions
        import math

        # Safe evaluation - only allow basic math operations and math functions
        allowed_chars = set('0123456789+-*/.() abcdefghijklmnopqrstuvwxyz')
        if not all(c.lower() in allowed_chars for c in expression):
            return "❌ Error: Only basic mathematical operations and functions are allowed"

        # Replace common mathematical functions
        safe_expression = expression.lower()
        safe_expression = safe_expression.replace('sqrt', 'math.sqrt')
        safe_expression = safe_expression.replace('sin', 'math.sin')
        safe_expression = safe_expression.replace('cos', 'math.cos')
        safe_expression = safe_expression.replace('tan', 'math.tan')
        safe_expression = safe_expression.replace('log', 'math.log')
        safe_expression = safe_expression.replace('pi', 'math.pi')
        safe_expression = safe_expression.replace('e', 'math.e')

        # Create safe namespace
        safe_dict = {
            "__builtins__": {},
            "math": math
        }

        result = eval(safe_expression, safe_dict)

        # Format result nicely
        if isinstance(result, float):
            if result.is_integer():
                result = int(result)
            else:
                result = round(result, 6)

        execution_time = time.time() - start_time
        track_tool_usage("calculator", execution_time)

        return f"🧮 Calculation: {expression} = {result}"

    except ZeroDivisionError:
        return "❌ Error: Division by zero is not allowed"
    except Exception as e:
        return f"❌ Error: Invalid mathematical expression - {str(e)}"

# Enhanced Student Finder Tool
@function_tool("piaic_student_finder")
def student_finder(student_roll: int) -> str:
    """
    Find PIAIC student information based on roll number with enhanced data.

    Args:
        student_roll: Student roll number

    Returns:
        Detailed student information or "Not Found" message
    """
    start_time = time.time()

    explain_tool("student_finder", "Searches PIAIC student database by roll number")

    # Enhanced student database
    students_data = {
        1: {
            "name": "Qasim Hassan",
            "program": "AI & Data Science",
            "batch": "Q4-2024",
            "status": "Active",
            "email": "<EMAIL>",
            "phone": "+92-300-1234567",
            "city": "Karachi",
            "projects": ["Chatbot Development", "Data Analysis Dashboard"],
            "skills": ["Python", "Machine Learning", "Data Visualization"],
            "gpa": 3.8
        },
        2: {
            "name": "Sir Zia Khan",
            "program": "Instructor - AI & Cloud",
            "batch": "Faculty",
            "status": "Faculty",
            "email": "<EMAIL>",
            "phone": "+92-300-9876543",
            "city": "Karachi",
            "specialization": ["Artificial Intelligence", "Cloud Computing", "Web3"],
            "experience": "15+ years",
            "courses": ["AI Fundamentals", "Cloud Architecture"]
        },
        3: {
            "name": "Daniyal Nagori",
            "program": "Web3 & Metaverse",
            "batch": "Q4-2024",
            "status": "Active",
            "email": "<EMAIL>",
            "phone": "+92-301-2345678",
            "city": "Lahore",
            "projects": ["NFT Marketplace", "DeFi Protocol"],
            "skills": ["Solidity", "React", "Blockchain"],
            "gpa": 3.9
        },
        4: {
            "name": "Ahmed Ali",
            "program": "Cloud Computing",
            "batch": "Q3-2024",
            "status": "Graduated",
            "email": "<EMAIL>",
            "phone": "+92-302-3456789",
            "city": "Islamabad",
            "projects": ["Microservices Architecture", "DevOps Pipeline"],
            "skills": ["AWS", "Docker", "Kubernetes"],
            "gpa": 3.7,
            "job": "Cloud Engineer at TechCorp"
        },
        5: {
            "name": "Fatima Sheikh",
            "program": "AI & Data Science",
            "batch": "Q4-2024",
            "status": "Active",
            "email": "<EMAIL>",
            "phone": "+92-303-4567890",
            "city": "Karachi",
            "projects": ["Predictive Analytics", "Computer Vision App"],
            "skills": ["Python", "TensorFlow", "OpenCV"],
            "gpa": 4.0
        },
        6: {
            "name": "Hassan Raza",
            "program": "IoT & Robotics",
            "batch": "Q3-2024",
            "status": "Active",
            "email": "<EMAIL>",
            "phone": "+92-304-5678901",
            "city": "Faisalabad",
            "projects": ["Smart Home System", "Agricultural Robot"],
            "skills": ["Arduino", "Raspberry Pi", "C++"],
            "gpa": 3.6
        }
    }

    student = students_data.get(student_roll)

    execution_time = time.time() - start_time
    track_tool_usage("student_finder", execution_time)

    if student:
        # Format student information nicely
        info = f"👨‍🎓 Student Found (Roll #{student_roll}):\n"
        info += f"📛 Name: {student['name']}\n"
        info += f"📚 Program: {student['program']}\n"
        info += f"🎯 Batch: {student['batch']}\n"
        info += f"✅ Status: {student['status']}\n"
        info += f"📧 Email: {student['email']}\n"
        info += f"📱 Phone: {student['phone']}\n"
        info += f"🏙️ City: {student['city']}\n"

        if 'gpa' in student:
            info += f"📊 GPA: {student['gpa']}/4.0\n"

        if 'projects' in student:
            info += f"🚀 Projects: {', '.join(student['projects'])}\n"

        if 'skills' in student:
            info += f"💡 Skills: {', '.join(student['skills'])}\n"

        if 'specialization' in student:
            info += f"🎯 Specialization: {', '.join(student['specialization'])}\n"

        if 'experience' in student:
            info += f"⏰ Experience: {student['experience']}\n"

        if 'job' in student:
            info += f"💼 Current Job: {student['job']}\n"

        return info.strip()
    else:
        return f"❌ Student with roll number {student_roll} not found in PIAIC database.\n💡 Available roll numbers: 1-6"

# Enhanced Weather Tool
@function_tool("get_weather")
def get_weather(location: str, unit: str = "C") -> str:
    """
    Fetch weather information for a given location with realistic simulation.

    Args:
        location: The city or location name
        unit: Temperature unit ('C' for Celsius, 'F' for Fahrenheit)

    Returns:
        Detailed weather description string
    """
    start_time = time.time()

    explain_tool("get_weather", f"Fetches weather information for {location}")

    # Simulate realistic weather data based on location
    weather_patterns = {
        "karachi": {"base_temp": 28, "conditions": ["sunny", "hot", "humid"], "humidity_range": (60, 85)},
        "lahore": {"base_temp": 25, "conditions": ["sunny", "cloudy", "smoggy"], "humidity_range": (45, 70)},
        "islamabad": {"base_temp": 22, "conditions": ["pleasant", "cloudy", "rainy"], "humidity_range": (50, 75)},
        "peshawar": {"base_temp": 24, "conditions": ["dry", "sunny", "windy"], "humidity_range": (35, 60)},
        "quetta": {"base_temp": 18, "conditions": ["cold", "dry", "windy"], "humidity_range": (25, 50)},
        "multan": {"base_temp": 30, "conditions": ["hot", "dry", "sunny"], "humidity_range": (40, 65)},
        "faisalabad": {"base_temp": 26, "conditions": ["warm", "cloudy", "humid"], "humidity_range": (55, 80)},
    }

    # Default pattern for unknown cities
    default_pattern = {"base_temp": 25, "conditions": ["partly cloudy", "mild"], "humidity_range": (50, 70)}

    location_lower = location.lower()
    pattern = weather_patterns.get(location_lower, default_pattern)

    # Generate weather data
    condition = random.choice(pattern["conditions"])
    base_temp = pattern["base_temp"]
    temp_variation = random.randint(-5, 8)

    if unit.upper() == "F":
        temp = int((base_temp + temp_variation) * 9/5 + 32)
        unit_symbol = "°F"
    else:
        temp = base_temp + temp_variation
        unit_symbol = "°C"

    humidity = random.randint(*pattern["humidity_range"])
    wind_speed = random.randint(5, 25)
    pressure = random.randint(1010, 1025)
    visibility = random.randint(8, 15)

    # Add weather emoji based on condition
    weather_emoji = {
        "sunny": "☀️", "hot": "🌡️", "cloudy": "☁️", "rainy": "🌧️",
        "humid": "💧", "dry": "🏜️", "windy": "💨", "cold": "❄️",
        "pleasant": "🌤️", "smoggy": "🌫️", "partly cloudy": "⛅"
    }
    emoji = weather_emoji.get(condition, "🌤️")

    execution_time = time.time() - start_time
    track_tool_usage("get_weather", execution_time)

    return (
        f"{emoji} Weather in {location.title()}:\n"
        f"🌡️ Temperature: {temp}{unit_symbol}\n"
        f"☁️ Condition: {condition.title()}\n"
        f"💧 Humidity: {humidity}%\n"
        f"💨 Wind Speed: {wind_speed} km/h\n"
        f"📊 Pressure: {pressure} hPa\n"
        f"👁️ Visibility: {visibility} km\n"
        f"📅 Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n"
        f"🌍 Coordinates: {random.uniform(24, 37):.2f}°N, {random.uniform(61, 75):.2f}°E"
    )

# Enhanced File Operations Tool
@function_tool("file_operations")
def file_operations(operation: str, filename: str, content: str = "") -> str:
    """
    Perform safe file operations with enhanced security and features.

    Args:
        operation: Type of operation ('read', 'write', 'list', 'info', 'search')
        filename: Name of the file
        content: Content to write (only for 'write' operation)

    Returns:
        Operation result or error message
    """
    start_time = time.time()

    if not config.allow_file_operations:
        return "❌ File operations are disabled in configuration"

    explain_tool("file_operations", f"Performing {operation} operation on files")

    try:
        if operation == "list":
            files = []
            for item in os.listdir("."):
                if os.path.isfile(item):
                    size = os.path.getsize(item)
                    modified = datetime.fromtimestamp(os.path.getmtime(item)).strftime('%Y-%m-%d %H:%M')
                    files.append(f"📄 {item} ({size} bytes, modified: {modified})")
                elif os.path.isdir(item):
                    files.append(f"📁 {item}/")

            execution_time = time.time() - start_time
            track_tool_usage("file_operations", execution_time)

            return f"📁 Contents of current directory ({len(files)} items):\n" + "\n".join(files[:20])

        elif operation == "read":
            if not os.path.exists(filename):
                return f"❌ File '{filename}' not found"

            # Check file extension
            file_ext = Path(filename).suffix.lower()
            if file_ext not in config.allowed_file_extensions:
                return f"❌ File type '{file_ext}' not allowed. Allowed types: {', '.join(config.allowed_file_extensions)}"

            # Check file size
            file_size = os.path.getsize(filename)
            if file_size > config.max_file_size_mb * 1024 * 1024:
                return f"❌ File too large ({file_size} bytes). Maximum allowed: {config.max_file_size_mb}MB"

            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
                if len(content) > 1000:
                    content = content[:1000] + "\n... (truncated)"

            execution_time = time.time() - start_time
            track_tool_usage("file_operations", execution_time)

            return f"📖 Content of {filename} ({file_size} bytes):\n{'-'*40}\n{content}"

        elif operation == "write":
            # Check file extension
            file_ext = Path(filename).suffix.lower()
            if file_ext not in config.allowed_file_extensions:
                return f"❌ File type '{file_ext}' not allowed. Allowed types: {', '.join(config.allowed_file_extensions)}"

            # Check content size
            content_size = len(content.encode('utf-8'))
            if content_size > config.max_file_size_mb * 1024 * 1024:
                return f"❌ Content too large ({content_size} bytes). Maximum allowed: {config.max_file_size_mb}MB"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)

            execution_time = time.time() - start_time
            track_tool_usage("file_operations", execution_time)

            return f"✅ Successfully wrote {content_size} bytes to '{filename}'"

        elif operation == "info":
            if not os.path.exists(filename):
                return f"❌ File '{filename}' not found"

            stat = os.stat(filename)
            size = stat.st_size
            created = datetime.fromtimestamp(stat.st_ctime).strftime('%Y-%m-%d %H:%M:%S')
            modified = datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')

            execution_time = time.time() - start_time
            track_tool_usage("file_operations", execution_time)

            return (
                f"📋 File Information for '{filename}':\n"
                f"📏 Size: {size} bytes ({size/1024:.2f} KB)\n"
                f"📅 Created: {created}\n"
                f"🔄 Modified: {modified}\n"
                f"📝 Extension: {Path(filename).suffix}\n"
                f"🔒 Readable: {os.access(filename, os.R_OK)}\n"
                f"✏️ Writable: {os.access(filename, os.W_OK)}"
            )

        elif operation == "search":
            if not content:
                return "❌ Search term is required for search operation"

            matching_files = []
            for file in os.listdir("."):
                if os.path.isfile(file):
                    file_ext = Path(file).suffix.lower()
                    if file_ext in config.allowed_file_extensions:
                        try:
                            with open(file, 'r', encoding='utf-8') as f:
                                file_content = f.read()
                                if content.lower() in file_content.lower():
                                    matching_files.append(file)
                        except:
                            continue

            execution_time = time.time() - start_time
            track_tool_usage("file_operations", execution_time)

            if matching_files:
                return f"🔍 Found '{content}' in {len(matching_files)} files:\n" + "\n".join([f"📄 {f}" for f in matching_files])
            else:
                return f"🔍 No files found containing '{content}'"

        else:
            return "❌ Invalid operation. Use 'read', 'write', 'list', 'info', or 'search'"

    except Exception as e:
        return f"❌ File operation error: {str(e)}"

# Enhanced Web Search Simulation Tool
@function_tool("web_search")
def web_search(query: str, max_results: int = 5) -> str:
    """
    Simulate web search results with enhanced realism and educational content.

    Args:
        query: Search query
        max_results: Maximum number of results to return (1-10)

    Returns:
        Simulated search results with educational content
    """
    start_time = time.time()

    explain_tool("web_search", f"Searching the web for: {query}")

    # Limit max_results
    max_results = min(max(max_results, 1), 10)

    # Enhanced search results database
    search_database = {
        "python": [
            "🐍 Python.org - Official Python Programming Language Website",
            "📚 Python Tutorial - Learn Python Step by Step",
            "🔧 Python Package Index (PyPI) - Find and Install Python Packages",
            "📖 Real Python - Python Tutorials and Articles",
            "🎓 Python for Beginners - Microsoft Learn",
            "💻 Python Documentation - Official Docs",
            "🚀 Awesome Python - Curated List of Python Resources"
        ],
        "ai": [
            "🤖 OpenAI - Artificial Intelligence Research Company",
            "🧠 Machine Learning Mastery - ML Tutorials and Guides",
            "⚡ TensorFlow - Open Source Machine Learning Platform",
            "🔥 PyTorch - Deep Learning Framework",
            "📊 Kaggle - Data Science and ML Competitions",
            "🎯 Towards Data Science - AI and ML Articles",
            "🏫 MIT OpenCourseWare - Artificial Intelligence"
        ],
        "weather": [
            "🌤️ Weather.com - Current Weather and Forecasts",
            "📊 AccuWeather - Weather Forecasts and Radar",
            "🌍 Weather Underground - Local Weather Conditions",
            "🛰️ National Weather Service - Official Weather Data",
            "📱 Weather Apps - Best Weather Applications",
            "🌡️ Climate Data - Historical Weather Information"
        ],
        "programming": [
            "💻 Stack Overflow - Programming Q&A Community",
            "📚 GitHub - Code Repository and Collaboration",
            "🎓 freeCodeCamp - Learn Programming for Free",
            "📖 MDN Web Docs - Web Development Resources",
            "🔧 Visual Studio Code - Code Editor",
            "🚀 Codecademy - Interactive Programming Courses"
        ],
        "data science": [
            "📊 Pandas Documentation - Data Analysis Library",
            "📈 Matplotlib - Python Plotting Library",
            "🔢 NumPy - Numerical Computing Library",
            "🧮 Jupyter Notebook - Interactive Computing",
            "📋 Seaborn - Statistical Data Visualization",
            "🎯 Scikit-learn - Machine Learning Library"
        ],
        "web development": [
            "🌐 MDN Web Docs - Web Development Guide",
            "⚛️ React Documentation - JavaScript Library",
            "💚 Node.js - JavaScript Runtime",
            "🎨 CSS-Tricks - Web Design and Development",
            "📱 Bootstrap - CSS Framework",
            "🚀 Netlify - Web Hosting and Deployment"
        ]
    }

    # Find relevant results
    results = []
    query_lower = query.lower()

    # Check for exact matches first
    for keyword, urls in search_database.items():
        if keyword in query_lower:
            results.extend(urls)

    # If no exact matches, look for partial matches
    if not results:
        for keyword, urls in search_database.items():
            if any(word in keyword for word in query_lower.split()):
                results.extend(urls[:3])  # Limit partial matches

    # If still no results, generate generic ones
    if not results:
        results = [
            f"🔍 '{query}' - Wikipedia Article",
            f"📄 Learn about '{query}' - Educational Resource",
            f"💡 '{query}' Tutorial - Step by Step Guide",
            f"📚 '{query}' Documentation - Official Docs",
            f"🎓 '{query}' Course - Online Learning Platform"
        ]

    # Remove duplicates and limit results
    results = list(dict.fromkeys(results))[:max_results]

    execution_time = time.time() - start_time
    track_tool_usage("web_search", execution_time)

    search_summary = (
        f"🔍 Search Results for '{query}' ({len(results)} results found in {execution_time:.2f}s):\n"
        f"{'='*60}\n"
    )

    for i, result in enumerate(results, 1):
        search_summary += f"{i}. {result}\n"

    search_summary += f"\n💡 Tip: These are simulated results for learning purposes."

    return search_summary

# Learning Analytics Tool
@function_tool("learning_analytics")
def learning_analytics(action: str = "summary") -> str:
    """
    Provide learning analytics and insights about tool usage.

    Args:
        action: Type of analytics ('summary', 'detailed', 'reset')

    Returns:
        Analytics report or confirmation message
    """
    start_time = time.time()

    if not config.enable_analytics:
        return "📊 Analytics are disabled in configuration"

    explain_tool("learning_analytics", "Analyzing your learning progress and tool usage")

    if action == "reset":
        tool_usage_stats.clear()
        return "🔄 Analytics data has been reset"

    if not tool_usage_stats:
        return "📊 No tool usage data available yet. Start using tools to see analytics!"

    total_usage = sum(stats["count"] for stats in tool_usage_stats.values())

    if action == "summary":
        most_used = max(tool_usage_stats.items(), key=lambda x: x[1]["count"])
        fastest_tool = min(tool_usage_stats.items(), key=lambda x: x[1]["average_time"])

        execution_time = time.time() - start_time
        track_tool_usage("learning_analytics", execution_time)

        return (
            f"📊 Learning Analytics Summary:\n"
            f"{'='*40}\n"
            f"🔧 Total Tool Uses: {total_usage}\n"
            f"🏆 Most Used Tool: {most_used[0]} ({most_used[1]['count']} times)\n"
            f"⚡ Fastest Tool: {fastest_tool[0]} ({fastest_tool[1]['average_time']:.3f}s avg)\n"
            f"📈 Tools Explored: {len(tool_usage_stats)}\n"
            f"📅 Session Started: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n"
            f"\n💡 Keep exploring different tools to enhance your learning!"
        )

    elif action == "detailed":
        report = f"📊 Detailed Learning Analytics:\n{'='*50}\n"

        for tool_name, stats in sorted(tool_usage_stats.items(), key=lambda x: x[1]["count"], reverse=True):
            report += (
                f"\n🔧 {tool_name.upper()}:\n"
                f"   📈 Usage Count: {stats['count']}\n"
                f"   ⏱️ Total Time: {stats['total_time']:.3f}s\n"
                f"   ⚡ Average Time: {stats['average_time']:.3f}s\n"
                f"   📅 Last Used: {stats['last_used']}\n"
            )

        execution_time = time.time() - start_time
        track_tool_usage("learning_analytics", execution_time)

        return report

    else:
        return "❌ Invalid action. Use 'summary', 'detailed', or 'reset'"

# System Information Tool
@function_tool("system_info")
def system_info(info_type: str = "basic") -> str:
    """
    Get system information for learning about the environment.

    Args:
        info_type: Type of information ('basic', 'python', 'config')

    Returns:
        System information report
    """
    start_time = time.time()

    explain_tool("system_info", f"Gathering {info_type} system information")

    if info_type == "basic":
        import platform

        execution_time = time.time() - start_time
        track_tool_usage("system_info", execution_time)

        return (
            f"💻 System Information:\n"
            f"{'='*30}\n"
            f"🖥️ OS: {platform.system()} {platform.release()}\n"
            f"🏗️ Architecture: {platform.architecture()[0]}\n"
            f"🐍 Python Version: {platform.python_version()}\n"
            f"💾 Working Directory: {os.getcwd()}\n"
            f"👤 User: {os.getenv('USER', os.getenv('USERNAME', 'Unknown'))}\n"
            f"📅 Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"🌍 Timezone: {time.tzname[0]}"
        )

    elif info_type == "python":
        import sys

        execution_time = time.time() - start_time
        track_tool_usage("system_info", execution_time)

        return (
            f"🐍 Python Environment:\n"
            f"{'='*30}\n"
            f"📍 Python Path: {sys.executable}\n"
            f"📦 Python Version: {sys.version}\n"
            f"📚 Module Search Paths: {len(sys.path)} paths\n"
            f"🔧 Platform: {sys.platform}\n"
            f"📊 Max Integer: {sys.maxsize}\n"
            f"🧠 Recursion Limit: {sys.getrecursionlimit()}\n"
            f"📝 Default Encoding: {sys.getdefaultencoding()}"
        )

    elif info_type == "config":
        execution_time = time.time() - start_time
        track_tool_usage("system_info", execution_time)

        config_info = config.to_dict()
        report = f"⚙️ Application Configuration:\n{'='*35}\n"

        for key, value in config_info.items():
            report += f"🔧 {key}: {value}\n"

        return report

    else:
        return "❌ Invalid info_type. Use 'basic', 'python', or 'config'"

# =============================================================================
# AGENT CREATION AND MANAGEMENT
# =============================================================================

class AgentManager:
    """Manages different AI agent personalities and configurations"""

    def __init__(self):
        self.agents = {}
        self.conversation_history = []
        self.current_agent = None
        self._create_agents()

    def _create_agents(self):
        """Create different agent personalities"""

        # All available tools
        all_tools = [
            calculator,
            student_finder,
            get_weather,
            file_operations,
            web_search,
            learning_analytics,
            system_info
        ]

        # Agent personalities with different instructions
        agent_configs = {
            "helpful": {
                "name": "Helpful Assistant",
                "instructions": (
                    "You are a helpful and knowledgeable AI assistant. "
                    "Provide clear, accurate, and detailed responses. "
                    "Use the available tools when appropriate to give the best answers. "
                    "Always explain what tools you're using and why. "
                    "Be educational and help users learn."
                ),
                "tools": all_tools
            },

            "teacher": {
                "name": "AI Teacher",
                "instructions": (
                    "You are an experienced teacher and mentor. "
                    "Your goal is to help students learn and understand concepts. "
                    "Always provide educational context and explanations. "
                    "Break down complex topics into simple steps. "
                    "Encourage learning and curiosity. "
                    "Use tools to demonstrate practical examples."
                ),
                "tools": all_tools
            },

            "technical": {
                "name": "Technical Expert",
                "instructions": (
                    "You are a technical expert and software engineer. "
                    "Provide detailed technical explanations with code examples when relevant. "
                    "Use precise terminology and include implementation details. "
                    "Focus on best practices and efficient solutions. "
                    "Help users understand the technical aspects of tools and systems."
                ),
                "tools": all_tools
            },

            "friendly": {
                "name": "Friendly Companion",
                "instructions": (
                    "You are a friendly and enthusiastic AI companion! "
                    "Use emojis and casual language to make interactions fun and engaging. "
                    "Always be positive and encouraging in your responses. "
                    "Make learning enjoyable and celebrate user achievements. "
                    "Be supportive and motivating."
                ),
                "tools": all_tools
            },

            "researcher": {
                "name": "Research Assistant",
                "instructions": (
                    "You are a thorough research assistant. "
                    "Provide comprehensive information and multiple perspectives. "
                    "Use tools to gather and analyze data systematically. "
                    "Present findings in a structured and organized manner. "
                    "Help users understand research methodologies and data analysis."
                ),
                "tools": all_tools
            },

            "tutor": {
                "name": "Personal Tutor",
                "instructions": (
                    "You are a patient and adaptive personal tutor. "
                    "Adjust your teaching style based on the user's level and needs. "
                    "Provide step-by-step guidance and practice opportunities. "
                    "Use tools to create interactive learning experiences. "
                    "Track progress and provide constructive feedback."
                ),
                "tools": all_tools
            }
        }

        # Create agents
        for agent_type, config in agent_configs.items():
            try:
                agent = Agent(
                    name=config["name"],
                    instructions=config["instructions"],
                    tools=config["tools"],
                    model=config.model
                )
                self.agents[agent_type] = agent
                logger.info(f"✅ Created {config['name']} agent")
            except Exception as e:
                logger.error(f"❌ Failed to create {agent_type} agent: {e}")

        # Set default agent
        self.current_agent = self.agents.get("helpful")
        logger.info(f"🤖 Default agent set to: {self.current_agent.name if self.current_agent else 'None'}")

    def get_agent(self, agent_type: str = None) -> Agent:
        """Get an agent by type"""
        if agent_type is None:
            return self.current_agent

        if agent_type in self.agents:
            return self.agents[agent_type]
        else:
            available = ", ".join(self.agents.keys())
            raise ValueError(f"Agent type '{agent_type}' not found. Available: {available}")

    def set_current_agent(self, agent_type: str):
        """Set the current active agent"""
        if agent_type in self.agents:
            self.current_agent = self.agents[agent_type]
            logger.info(f"🔄 Switched to {self.current_agent.name}")
            return f"✅ Switched to {self.current_agent.name}"
        else:
            available = ", ".join(self.agents.keys())
            return f"❌ Agent type '{agent_type}' not found. Available: {available}"

    def list_agents(self) -> str:
        """List all available agents"""
        if not self.agents:
            return "❌ No agents available"

        agent_list = "🤖 Available AI Agents:\n" + "="*30 + "\n"
        for agent_type, agent in self.agents.items():
            status = "🟢 (Current)" if agent == self.current_agent else "⚪"
            agent_list += f"{status} {agent_type}: {agent.name}\n"

        return agent_list

    def add_to_history(self, query: str, response: str, agent_name: str):
        """Add interaction to conversation history"""
        if len(self.conversation_history) >= config.max_conversation_history:
            self.conversation_history.pop(0)  # Remove oldest entry

        self.conversation_history.append({
            "timestamp": datetime.now().isoformat(),
            "agent": agent_name,
            "query": query,
            "response": response
        })

    def get_history(self, limit: int = 10) -> str:
        """Get conversation history"""
        if not self.conversation_history:
            return "📝 No conversation history available"

        history = f"📝 Conversation History (Last {min(limit, len(self.conversation_history))} entries):\n"
        history += "="*60 + "\n"

        for entry in self.conversation_history[-limit:]:
            timestamp = datetime.fromisoformat(entry["timestamp"]).strftime("%H:%M:%S")
            history += f"\n[{timestamp}] 🤖 {entry['agent']}:\n"
            history += f"❓ Q: {entry['query'][:100]}{'...' if len(entry['query']) > 100 else ''}\n"
            history += f"💬 A: {entry['response'][:200]}{'...' if len(entry['response']) > 200 else ''}\n"

        return history

# Create global agent manager
agent_manager = AgentManager()

# =============================================================================
# MAIN APPLICATION FUNCTIONS
# =============================================================================

async def run_single_query(query: str, agent_type: str = None, verbose: bool = False) -> str:
    """
    Run a single query with the specified agent

    Args:
        query: The query to ask
        agent_type: Type of agent to use (optional)
        verbose: Whether to show detailed information

    Returns:
        The agent's response
    """
    try:
        agent = agent_manager.get_agent(agent_type)

        if verbose:
            print(f"\n🤖 Using Agent: {agent.name}")
            print(f"❓ Query: {query}")
            print(f"⏱️ Processing...")

        start_time = time.time()
        result = await Runner.run(agent, query)
        execution_time = time.time() - start_time

        response = result.final_output

        # Add to conversation history
        agent_manager.add_to_history(query, response, agent.name)

        if verbose:
            print(f"✅ Completed in {execution_time:.2f} seconds")

        return response

    except Exception as e:
        error_msg = f"❌ Error processing query: {str(e)}"
        logger.error(error_msg)
        return error_msg

async def interactive_chat():
    """Start an interactive chat session"""
    print("\n" + "="*60)
    print("🚀 Welcome to Enhanced AI Agents Interactive Chat!")
    print("="*60)
    print(f"🤖 Current Agent: {agent_manager.current_agent.name}")
    print("\n💡 Available Commands:")
    print("  /help          - Show this help message")
    print("  /agents        - List available agents")
    print("  /switch <type> - Switch to different agent")
    print("  /history       - Show conversation history")
    print("  /analytics     - Show learning analytics")
    print("  /clear         - Clear screen")
    print("  /quit or /exit - Exit the chat")
    print("\n🎯 Type your questions or use commands starting with '/'")
    print("="*60)

    while True:
        try:
            user_input = input(f"\n[{agent_manager.current_agent.name}] You: ").strip()

            if not user_input:
                continue

            # Handle commands
            if user_input.startswith('/'):
                command = user_input[1:].lower().split()

                if command[0] in ['quit', 'exit', 'bye']:
                    print("\n👋 Thank you for using AI Agents! Goodbye!")
                    break

                elif command[0] == 'help':
                    print("\n💡 Available Commands:")
                    print("  /help          - Show this help message")
                    print("  /agents        - List available agents")
                    print("  /switch <type> - Switch to different agent")
                    print("  /history       - Show conversation history")
                    print("  /analytics     - Show learning analytics")
                    print("  /clear         - Clear screen")
                    print("  /quit or /exit - Exit the chat")

                elif command[0] == 'agents':
                    print(f"\n{agent_manager.list_agents()}")

                elif command[0] == 'switch':
                    if len(command) > 1:
                        result = agent_manager.set_current_agent(command[1])
                        print(f"\n{result}")
                    else:
                        print("\n❌ Please specify agent type. Use '/agents' to see available options.")

                elif command[0] == 'history':
                    print(f"\n{agent_manager.get_history()}")

                elif command[0] == 'analytics':
                    analytics_result = learning_analytics("summary")
                    print(f"\n{analytics_result}")

                elif command[0] == 'clear':
                    os.system('cls' if os.name == 'nt' else 'clear')

                else:
                    print(f"\n❌ Unknown command: /{command[0]}. Type '/help' for available commands.")

                continue

            # Process regular queries
            print(f"\n🤖 {agent_manager.current_agent.name}: ", end="", flush=True)
            response = await run_single_query(user_input, verbose=False)
            print(response)

        except KeyboardInterrupt:
            print("\n\n👋 Chat interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {str(e)}")
            logger.error(f"Chat error: {e}")

async def run_demo_scenarios():
    """Run predefined demo scenarios to showcase capabilities"""
    print("\n🎬 Running Demo Scenarios...")
    print("="*50)

    demo_scenarios = [
        {
            "name": "Weather Information",
            "agent": "helpful",
            "query": "What's the weather like in Karachi and Lahore?",
            "description": "Demonstrates weather tool with multiple locations"
        },
        {
            "name": "Student Database Search",
            "agent": "teacher",
            "query": "Find information about PIAIC students with roll numbers 1, 2, and 3",
            "description": "Shows student finder tool with educational context"
        },
        {
            "name": "Mathematical Calculations",
            "agent": "technical",
            "query": "Calculate the area of a circle with radius 5, and find the square root of 144",
            "description": "Demonstrates enhanced calculator with mathematical functions"
        },
        {
            "name": "Web Search Simulation",
            "agent": "researcher",
            "query": "Search for information about Python programming and AI",
            "description": "Shows web search tool with research context"
        },
        {
            "name": "File Operations",
            "agent": "technical",
            "query": "List the files in the current directory and show system information",
            "description": "Demonstrates file operations and system info tools"
        },
        {
            "name": "Learning Analytics",
            "agent": "tutor",
            "query": "Show me my learning analytics and progress summary",
            "description": "Displays learning progress and tool usage statistics"
        }
    ]

    for i, scenario in enumerate(demo_scenarios, 1):
        print(f"\n📋 Demo {i}: {scenario['name']}")
        print(f"📝 Description: {scenario['description']}")
        print(f"🤖 Agent: {scenario['agent']}")
        print(f"❓ Query: {scenario['query']}")
        print("-" * 50)

        try:
            response = await run_single_query(scenario['query'], scenario['agent'], verbose=True)
            print(f"\n💬 Response:\n{response}")
        except Exception as e:
            print(f"\n❌ Demo failed: {e}")

        print("\n" + "="*50)

        # Small delay between demos
        await asyncio.sleep(1)

    print("\n🎉 Demo scenarios completed!")
    print(f"\n📊 Final Analytics:")
    analytics_result = learning_analytics("summary")
    print(analytics_result)

async def run_tutorial():
    """Run an interactive tutorial for new users"""
    print("\n🎓 Welcome to the AI Agents Tutorial!")
    print("="*40)
    print("This tutorial will guide you through the features of our AI agents system.")

    tutorial_steps = [
        {
            "title": "Introduction to AI Agents",
            "content": "AI agents are intelligent assistants that can use tools to help you with various tasks.",
            "query": "What can you tell me about AI agents and their capabilities?",
            "agent": "teacher"
        },
        {
            "title": "Using the Calculator Tool",
            "content": "Let's try some mathematical calculations using the calculator tool.",
            "query": "Calculate 15 * 8 + sqrt(64) - 10",
            "agent": "technical"
        },
        {
            "title": "Weather Information",
            "content": "The weather tool can provide information about different locations.",
            "query": "What's the weather like in Islamabad?",
            "agent": "helpful"
        },
        {
            "title": "Student Database",
            "content": "You can search for student information using roll numbers.",
            "query": "Find student with roll number 5",
            "agent": "teacher"
        },
        {
            "title": "Learning Analytics",
            "content": "Track your progress and see which tools you've been using.",
            "query": "Show me my learning analytics",
            "agent": "tutor"
        }
    ]

    for i, step in enumerate(tutorial_steps, 1):
        print(f"\n📚 Step {i}: {step['title']}")
        print(f"💡 {step['content']}")
        print(f"🤖 Using {step['agent']} agent")

        input("\n⏸️ Press Enter to continue...")

        print(f"\n❓ Example Query: {step['query']}")
        response = await run_single_query(step['query'], step['agent'], verbose=True)
        print(f"\n💬 Response:\n{response}")

        print("\n" + "-"*50)

    print("\n🎉 Tutorial completed! You're now ready to use the AI agents system.")
    print("💡 Try the interactive chat mode with: python main.py")

def print_welcome_banner():
    """Print a welcome banner with system information"""
    print("\n" + "="*70)
    print("🤖 ENHANCED AI AGENTS SYSTEM")
    print("="*70)
    print(f"🚀 Environment: {config.environment}")
    print(f"🧠 Model: {config.default_model}")
    print(f"🔧 Available Agents: {len(agent_manager.agents)}")
    print(f"🛠️ Available Tools: 7")
    print(f"📊 Analytics: {'Enabled' if config.enable_analytics else 'Disabled'}")
    print(f"🎓 Tutorial Mode: {'Enabled' if config.enable_tutorial_mode else 'Disabled'}")
    print("="*70)

def setup_argument_parser():
    """Setup command line argument parser"""
    parser = argparse.ArgumentParser(
        description="Enhanced AI Agents System - Learn and interact with AI agents using various tools",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                           # Interactive chat mode
  python main.py --demo                    # Run demo scenarios
  python main.py --tutorial               # Start tutorial mode
  python main.py --query "weather in NYC" # Single query
  python main.py --agent teacher --query "explain AI" # Use specific agent
  python main.py --analytics              # Show analytics only
        """
    )

    parser.add_argument(
        '--query', '-q',
        type=str,
        help='Run a single query and exit'
    )

    parser.add_argument(
        '--agent', '-a',
        type=str,
        choices=list(agent_manager.agents.keys()),
        default='helpful',
        help='Specify which agent to use (default: helpful)'
    )

    parser.add_argument(
        '--demo', '-d',
        action='store_true',
        help='Run demo scenarios showcasing all features'
    )

    parser.add_argument(
        '--tutorial', '-t',
        action='store_true',
        help='Start interactive tutorial for new users'
    )

    parser.add_argument(
        '--analytics',
        action='store_true',
        help='Show learning analytics and exit'
    )

    parser.add_argument(
        '--list-agents',
        action='store_true',
        help='List all available agents and exit'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )

    parser.add_argument(
        '--no-banner',
        action='store_true',
        help='Skip the welcome banner'
    )

    return parser

async def main():
    """Main application entry point"""
    parser = setup_argument_parser()
    args = parser.parse_args()

    # Print welcome banner unless disabled
    if not args.no_banner:
        print_welcome_banner()

    try:
        # Handle different modes
        if args.list_agents:
            print(agent_manager.list_agents())
            return

        if args.analytics:
            analytics_result = learning_analytics("detailed")
            print(analytics_result)
            return

        if args.tutorial:
            await run_tutorial()
            return

        if args.demo:
            await run_demo_scenarios()
            return

        if args.query:
            print(f"\n🤖 Processing query with {args.agent} agent...")
            response = await run_single_query(args.query, args.agent, args.verbose)
            print(f"\n💬 Response:\n{response}")

            if config.enable_analytics:
                print(f"\n📊 Quick Analytics:")
                analytics_result = learning_analytics("summary")
                print(analytics_result)
            return

        # Default: Interactive chat mode
        await interactive_chat()

    except KeyboardInterrupt:
        print("\n\n👋 Application interrupted. Goodbye!")
    except Exception as e:
        logger.error(f"Application error: {e}")
        print(f"\n❌ Application error: {e}")
        if config.debug:
            import traceback
            traceback.print_exc()
    finally:
        # Save analytics data if enabled
        if config.enable_analytics and tool_usage_stats:
            try:
                analytics_file = Path("logs") / "analytics.json"
                analytics_file.parent.mkdir(exist_ok=True)

                with open(analytics_file, 'w') as f:
                    json.dump({
                        "session_end": datetime.now().isoformat(),
                        "tool_usage_stats": tool_usage_stats,
                        "conversation_count": len(agent_manager.conversation_history)
                    }, f, indent=2)

                logger.info(f"Analytics saved to {analytics_file}")
            except Exception as e:
                logger.error(f"Failed to save analytics: {e}")

if __name__ == "__main__":
    # Ensure event loop compatibility
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

    # Run the main application
    asyncio.run(main())
