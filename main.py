from config import config
from agents import Agent, <PERSON>, function_tool

# Calculator Tool
@function_tool("calculator")
def calculator(expression: str) -> str:
    """
    Perform mathematical calculations safely.
    
    Args:
        expression: Mathematical expression to evaluate (e.g., "2 + 3 * 4")
    
    Returns:
        Calculation result or error message
    """
    try:
        # Safe evaluation - only allow basic math operations
        allowed_chars = set('0123456789+-*/.() ')
        if not all(char in allowed_chars for char in expression):
            return "❌ Error: Only basic mathematical operations are allowed (+, -, *, /, parentheses)"
        
        result = eval(expression)
        return f"🧮 Calculation: {expression} = {result}"
    except ZeroDivisionError:
        return "❌ Error: Division by zero is not allowed"
    except Exception as e:
        return f"❌ Error: Invalid mathematical expression - {str(e)}"

# Student Finder Tool
@function_tool("piaic_student_finder")
def student_finder(student_roll: int) -> str:
    """
    Find PIAIC student information based on roll number.
    
    Args:
        student_roll: Student roll number
    
    Returns:
        Student information or "Not Found" message
    """
    students_data = {
        1: {
            "name": "<PERSON><PERSON><PERSON>",
            "program": "AI & Data Science",
