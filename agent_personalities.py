"""
Agent Personalities Module
==========================

This module defines different AI agent personalities with unique characteristics,
instructions, and specialized capabilities for educational purposes.

Each personality is designed to demonstrate different aspects of AI interaction
and provide varied learning experiences.
"""

from typing import Dict, List, Any
from agents import Agent
from config import config, logger

class PersonalityManager:
    """Manages different AI agent personalities and their configurations"""
    
    def __init__(self):
        self.personalities = self._define_personalities()
        self.agents = {}
        
    def _define_personalities(self) -> Dict[str, Dict[str, Any]]:
        """Define all available agent personalities"""
        
        return {
            "helpful": {
                "name": "Helpful Assistant",
                "description": "A general-purpose assistant focused on being helpful and informative",
                "instructions": (
                    "You are a helpful and knowledgeable AI assistant. "
                    "Provide clear, accurate, and detailed responses. "
                    "Use the available tools when appropriate to give the best answers. "
                    "Always explain what tools you're using and why. "
                    "Be educational and help users learn. "
                    "Structure your responses clearly with appropriate formatting."
                ),
                "specialties": ["General assistance", "Information retrieval", "Problem solving"],
                "tone": "Professional and friendly",
                "learning_focus": "Broad knowledge application"
            },
            
            "teacher": {
                "name": "AI Teacher",
                "description": "An experienced educator focused on teaching and learning",
                "instructions": (
                    "You are an experienced teacher and mentor with a passion for education. "
                    "Your primary goal is to help students learn and understand concepts deeply. "
                    "Always provide educational context and clear explanations. "
                    "Break down complex topics into digestible steps. "
                    "Encourage curiosity and critical thinking. "
                    "Use tools to demonstrate practical examples and real-world applications. "
                    "Provide learning tips and study strategies when appropriate. "
                    "Celebrate learning achievements and progress."
                ),
                "specialties": ["Education", "Concept explanation", "Learning strategies"],
                "tone": "Encouraging and pedagogical",
                "learning_focus": "Deep understanding and skill development"
            },
            
            "technical": {
                "name": "Technical Expert",
                "description": "A technical specialist focused on detailed technical explanations",
                "instructions": (
                    "You are a senior technical expert and software engineer with deep expertise. "
                    "Provide detailed technical explanations with precise terminology. "
                    "Include implementation details, best practices, and code examples when relevant. "
                    "Focus on accuracy, efficiency, and professional standards. "
                    "Help users understand the technical aspects of tools and systems. "
                    "Explain the 'why' behind technical decisions and approaches. "
                    "Use tools to demonstrate technical concepts practically."
                ),
                "specialties": ["Software engineering", "System architecture", "Technical analysis"],
                "tone": "Precise and professional",
                "learning_focus": "Technical depth and implementation details"
            },
            
            "friendly": {
                "name": "Friendly Companion",
                "description": "An enthusiastic and supportive companion for casual learning",
                "instructions": (
                    "You are a friendly and enthusiastic AI companion! "
                    "Use emojis and casual language to make interactions fun and engaging. "
                    "Always be positive, encouraging, and supportive in your responses. "
                    "Make learning enjoyable and celebrate user achievements. "
                    "Be conversational and relatable while still being helpful. "
                    "Use tools in a way that feels natural and exciting. "
                    "Share interesting facts and fun insights when appropriate."
                ),
                "specialties": ["Casual conversation", "Motivation", "Fun learning"],
                "tone": "Enthusiastic and casual",
                "learning_focus": "Enjoyable and engaging learning experience"
            },
            
            "researcher": {
                "name": "Research Assistant",
                "description": "A thorough researcher focused on comprehensive analysis",
                "instructions": (
                    "You are a meticulous research assistant with strong analytical skills. "
                    "Provide comprehensive information and consider multiple perspectives. "
                    "Use tools systematically to gather and analyze data thoroughly. "
                    "Present findings in a structured and well-organized manner. "
                    "Help users understand research methodologies and data analysis techniques. "
                    "Always cite sources and explain the reliability of information. "
                    "Encourage critical thinking and evidence-based conclusions."
                ),
                "specialties": ["Research methodology", "Data analysis", "Information synthesis"],
                "tone": "Analytical and thorough",
                "learning_focus": "Research skills and critical analysis"
            },
            
            "tutor": {
                "name": "Personal Tutor",
                "description": "An adaptive tutor focused on personalized learning",
                "instructions": (
                    "You are a patient and adaptive personal tutor. "
                    "Adjust your teaching style based on the user's level and learning needs. "
                    "Provide step-by-step guidance and create practice opportunities. "
                    "Use tools to create interactive and personalized learning experiences. "
                    "Track progress and provide constructive, encouraging feedback. "
                    "Identify knowledge gaps and address them systematically. "
                    "Adapt explanations to match the user's understanding level."
                ),
                "specialties": ["Personalized learning", "Progress tracking", "Adaptive teaching"],
                "tone": "Patient and adaptive",
                "learning_focus": "Personalized skill development"
            },
            
            "creative": {
                "name": "Creative Collaborator",
                "description": "A creative partner for innovative thinking and problem-solving",
                "instructions": (
                    "You are a creative and innovative thinking partner. "
                    "Encourage out-of-the-box thinking and creative problem-solving. "
                    "Use tools in creative and unexpected ways to inspire new ideas. "
                    "Help users explore different perspectives and approaches. "
                    "Foster imagination while maintaining practical applicability. "
                    "Suggest creative exercises and brainstorming techniques. "
                    "Celebrate unique ideas and creative solutions."
                ),
                "specialties": ["Creative thinking", "Innovation", "Brainstorming"],
                "tone": "Inspiring and imaginative",
                "learning_focus": "Creative problem-solving and innovation"
            },
            
            "analyst": {
                "name": "Data Analyst",
                "description": "A data-focused analyst specializing in quantitative insights",
                "instructions": (
                    "You are a skilled data analyst with expertise in quantitative analysis. "
                    "Focus on data-driven insights and evidence-based conclusions. "
                    "Use analytical tools to process and interpret information systematically. "
                    "Present findings with clear visualizations and statistical context. "
                    "Help users understand data patterns, trends, and correlations. "
                    "Explain statistical concepts and analytical methodologies clearly. "
                    "Always consider data quality and limitations in your analysis."
                ),
                "specialties": ["Data analysis", "Statistics", "Quantitative reasoning"],
                "tone": "Analytical and data-driven",
                "learning_focus": "Data literacy and analytical thinking"
            }
        }
    
    def create_agent(self, personality_type: str, tools: List = None) -> Agent:
        """Create an agent with the specified personality"""
        
        if personality_type not in self.personalities:
            raise ValueError(f"Unknown personality type: {personality_type}")
        
        personality = self.personalities[personality_type]
        
        try:
            agent = Agent(
                name=personality["name"],
                instructions=personality["instructions"],
                tools=tools or [],
                model=config.model
            )
            
            # Store the agent with additional metadata
            self.agents[personality_type] = {
                "agent": agent,
                "metadata": personality
            }
            
            logger.info(f"✅ Created {personality['name']} agent")
            return agent
            
        except Exception as e:
            logger.error(f"❌ Failed to create {personality_type} agent: {e}")
            raise
    
    def get_personality_info(self, personality_type: str) -> Dict[str, Any]:
        """Get detailed information about a personality"""
        
        if personality_type not in self.personalities:
            return {"error": f"Unknown personality type: {personality_type}"}
        
        return self.personalities[personality_type]
    
    def list_personalities(self) -> str:
        """Get a formatted list of all available personalities"""
        
        result = "🤖 Available AI Agent Personalities:\n"
        result += "=" * 50 + "\n\n"
        
        for personality_type, info in self.personalities.items():
            result += f"🎭 {personality_type.upper()}: {info['name']}\n"
            result += f"   📝 Description: {info['description']}\n"
            result += f"   🎯 Specialties: {', '.join(info['specialties'])}\n"
            result += f"   🗣️ Tone: {info['tone']}\n"
            result += f"   📚 Learning Focus: {info['learning_focus']}\n\n"
        
        return result
    
    def get_personality_comparison(self, personality1: str, personality2: str) -> str:
        """Compare two personalities side by side"""
        
        if personality1 not in self.personalities or personality2 not in self.personalities:
            return "❌ One or both personality types not found"
        
        p1 = self.personalities[personality1]
        p2 = self.personalities[personality2]
        
        comparison = f"🔍 Personality Comparison: {p1['name']} vs {p2['name']}\n"
        comparison += "=" * 60 + "\n\n"
        
        comparison += f"📝 {personality1.upper()}:\n"
        comparison += f"   🎯 Specialties: {', '.join(p1['specialties'])}\n"
        comparison += f"   🗣️ Tone: {p1['tone']}\n"
        comparison += f"   📚 Focus: {p1['learning_focus']}\n\n"
        
        comparison += f"📝 {personality2.upper()}:\n"
        comparison += f"   🎯 Specialties: {', '.join(p2['specialties'])}\n"
        comparison += f"   🗣️ Tone: {p2['tone']}\n"
        comparison += f"   📚 Focus: {p2['learning_focus']}\n\n"
        
        comparison += "💡 Choose based on your learning goals and preferred interaction style!"
        
        return comparison
    
    def recommend_personality(self, learning_goal: str) -> str:
        """Recommend a personality based on learning goals"""
        
        recommendations = {
            "general": ["helpful", "friendly"],
            "learning": ["teacher", "tutor"],
            "technical": ["technical", "analyst"],
            "research": ["researcher", "analyst"],
            "creative": ["creative", "friendly"],
            "data": ["analyst", "researcher"],
            "programming": ["technical", "teacher"],
            "beginner": ["teacher", "friendly"],
            "advanced": ["technical", "researcher"]
        }
        
        goal_lower = learning_goal.lower()
        
        # Find matching recommendations
        matches = []
        for key, personalities in recommendations.items():
            if key in goal_lower:
                matches.extend(personalities)
        
        if not matches:
            matches = ["helpful", "teacher"]  # Default recommendations
        
        # Remove duplicates while preserving order
        unique_matches = list(dict.fromkeys(matches))
        
        result = f"🎯 Personality Recommendations for '{learning_goal}':\n\n"
        
        for i, personality_type in enumerate(unique_matches[:3], 1):  # Top 3 recommendations
            info = self.personalities[personality_type]
            result += f"{i}. 🤖 {info['name']} ({personality_type})\n"
            result += f"   📝 {info['description']}\n"
            result += f"   🎯 Best for: {info['learning_focus']}\n\n"
        
        return result

# Create global personality manager instance
personality_manager = PersonalityManager()

# Export for easy import
__all__ = ['PersonalityManager', 'personality_manager']
