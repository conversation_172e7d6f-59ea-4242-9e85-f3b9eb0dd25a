"""
Advanced Usage Examples
======================

This script demonstrates advanced features and usage patterns of the AI agents system.
Includes complex workflows, agent coordination, and advanced tool combinations.

Usage:
    python examples/advanced_examples.py
"""

import sys
import asyncio
import json
from pathlib import Path
from datetime import datetime

# Add parent directory to path to import modules
sys.path.append(str(Path(__file__).parent.parent))

from config import config, logger
from main import agent_manager, run_single_query
from learning_features import tutorial_system, skill_assessment
from utils import performance_monitor, Formatter

async def multi_agent_workflow():
    """Demonstrate coordinated multi-agent workflows"""
    
    print("🤝 Multi-Agent Workflow Example")
    print("=" * 45)
    
    # Scenario: Comprehensive data analysis project
    print("\n📊 Scenario: Comprehensive Data Analysis Project")
    print("-" * 50)
    
    # Step 1: Research phase
    print("\n🔍 Step 1: Research Phase (Research Agent)")
    research_response = await run_single_query(
        "Research data analysis best practices and methodologies",
        agent_type="researcher"
    )
    print(f"Research findings: {research_response[:300]}...")
    
    # Step 2: Technical analysis
    print("\n🔧 Step 2: Technical Analysis (Technical Agent)")
    technical_response = await run_single_query(
        "Analyze this dataset and explain the technical approach: [12, 15, 18, 22, 25, 28, 30, 35]",
        agent_type="technical"
    )
    print(f"Technical analysis: {technical_response[:300]}...")
    
    # Step 3: Educational explanation
    print("\n👩‍🏫 Step 3: Educational Explanation (Teacher Agent)")
    teaching_response = await run_single_query(
        "Explain data analysis concepts for beginners using the dataset [12, 15, 18, 22, 25, 28, 30, 35]",
        agent_type="teacher"
    )
    print(f"Educational content: {teaching_response[:300]}...")
    
    # Step 4: Creative presentation
    print("\n🎨 Step 4: Creative Presentation (Creative Agent)")
    creative_response = await run_single_query(
        "Create an engaging way to present data analysis results",
        agent_type="creative"
    )
    print(f"Creative ideas: {creative_response[:300]}...")
    
    print("\n✅ Multi-agent workflow completed!")

async def complex_tool_combinations():
    """Demonstrate complex tool combinations and workflows"""
    
    print("\n\n🔧 Complex Tool Combinations")
    print("=" * 40)
    
    # Mathematical analysis with multiple tools
    print("\n📐 Mathematical Analysis Workflow:")
    
    # Step 1: Advanced calculations
    calc_response = await run_single_query(
        "Calculate the mean, standard deviation, and variance for: [85, 90, 78, 92, 88, 76, 95, 89]",
        agent_type="analyst"
    )
    print(f"Calculations: {calc_response}")
    
    # Step 2: Statistical interpretation
    stats_response = await run_single_query(
        "Interpret statistical results and provide insights for student grades",
        agent_type="teacher"
    )
    print(f"Interpretation: {stats_response[:200]}...")
    
    # Step 3: System performance check
    system_response = await run_single_query(
        "Show system performance metrics for these calculations",
        agent_type="technical"
    )
    print(f"Performance: {system_response[:200]}...")

async def adaptive_learning_demo():
    """Demonstrate adaptive learning features"""
    
    print("\n\n🎓 Adaptive Learning Demo")
    print("=" * 35)
    
    # Simulate learning progression
    learning_queries = [
        ("What is data analysis?", "teacher", "beginner"),
        ("Calculate basic statistics for [1,2,3,4,5]", "tutor", "beginner"),
        ("Explain advanced statistical concepts", "technical", "intermediate"),
        ("Design a data analysis workflow", "researcher", "advanced")
    ]
    
    print("\n📈 Simulating Learning Progression:")
    for i, (query, agent_type, level) in enumerate(learning_queries, 1):
        print(f"\n📚 Learning Step {i} ({level} level):")
        response = await run_single_query(query, agent_type)
        print(f"Response: {response[:150]}...")
        
        # Simulate progress tracking
        print(f"✅ Progress: Step {i}/4 completed")

async def performance_optimization_demo():
    """Demonstrate performance monitoring and optimization"""
    
    print("\n\n⚡ Performance Optimization Demo")
    print("=" * 45)
    
    # Monitor performance across different operations
    operations = [
        ("Complex calculation: sqrt(144) + log(100) * sin(pi/2)", "technical"),
        ("Data analysis: [10,20,30,40,50,60,70,80,90,100]", "analyst"),
        ("Text analysis: 'Performance optimization in AI systems'", "technical"),
        ("Weather data for multiple cities", "helpful"),
        ("Student database comprehensive search", "teacher")
    ]
    
    print("\n🏃‍♂️ Running Performance Tests:")
    results = []
    
    for operation, agent_type in operations:
        print(f"\n🔄 Testing: {operation[:40]}...")
        
        start_time = asyncio.get_event_loop().time()
        response = await run_single_query(operation, agent_type)
        end_time = asyncio.get_event_loop().time()
        
        duration = end_time - start_time
        results.append({
            "operation": operation[:40],
            "agent": agent_type,
            "duration": duration,
            "success": "Error" not in response
        })
        
        print(f"⏱️ Completed in {duration:.3f}s")
    
    # Performance summary
    print("\n📊 Performance Summary:")
    total_time = sum(r["duration"] for r in results)
    avg_time = total_time / len(results)
    success_rate = sum(1 for r in results if r["success"]) / len(results) * 100
    
    print(f"🔢 Total Operations: {len(results)}")
    print(f"⏱️ Total Time: {Formatter.format_time(total_time)}")
    print(f"📊 Average Time: {Formatter.format_time(avg_time)}")
    print(f"✅ Success Rate: {success_rate:.1f}%")
    
    # Show detailed performance report
    performance_response = await run_single_query(
        "Show detailed system performance metrics",
        agent_type="technical"
    )
    print(f"\n📈 Detailed Metrics: {performance_response[:200]}...")

async def skill_assessment_demo():
    """Demonstrate skill assessment and recommendations"""
    
    print("\n\n🎯 Skill Assessment Demo")
    print("=" * 35)
    
    # Simulate tool usage for assessment
    print("\n📊 Simulating Tool Usage for Assessment:")
    
    # Use various tools to generate usage data
    assessment_queries = [
        ("Calculate 15 * 8 + 25", "helpful"),
        ("Analyze data: [5,10,15,20,25]", "analyst"),
        ("Process text: 'AI and machine learning'", "technical"),
        ("Find student 2", "teacher"),
        ("Search for Python information", "researcher"),
        ("Show system info", "technical")
    ]
    
    for query, agent_type in assessment_queries:
        await run_single_query(query, agent_type)
        print(f"✅ Used {agent_type} agent for: {query[:30]}...")
    
    # Generate skill assessment
    print("\n🎓 Generating Skill Assessment:")
    assessment_response = await run_single_query(
        "Provide a comprehensive skill assessment and recommendations",
        agent_type="tutor"
    )
    print(f"Assessment: {assessment_response}")

async def tutorial_system_demo():
    """Demonstrate tutorial system capabilities"""
    
    print("\n\n📚 Tutorial System Demo")
    print("=" * 35)
    
    # Show available tutorials
    print("\n📋 Available Tutorials:")
    tutorials = tutorial_system.get_available_tutorials()
    print(tutorials)
    
    # Simulate tutorial interaction
    print("\n🎓 Tutorial Interaction Simulation:")
    print("(In real usage, you would use the interactive tutorial system)")
    
    # Show tutorial recommendations
    recommendations = tutorial_system.get_next_recommendations()
    print(f"\n💡 Recommended Tutorials: {recommendations}")

async def error_recovery_demo():
    """Demonstrate error handling and recovery"""
    
    print("\n\n🛡️ Error Recovery Demo")
    print("=" * 30)
    
    # Test various error scenarios
    error_scenarios = [
        ("Calculate invalid_function(unknown)", "helpful"),
        ("Find student with roll number -1", "teacher"),
        ("Analyze empty dataset: []", "analyst"),
        ("Process malformed text: <<<>>>", "technical")
    ]
    
    print("\n⚠️ Testing Error Scenarios:")
    for scenario, agent_type in error_scenarios:
        print(f"\n🧪 Testing: {scenario}")
        try:
            response = await run_single_query(scenario, agent_type)
            print(f"Response: {response[:150]}...")
        except Exception as e:
            print(f"Handled error: {e}")

async def data_persistence_demo():
    """Demonstrate data persistence and session management"""
    
    print("\n\n💾 Data Persistence Demo")
    print("=" * 35)
    
    # Show conversation history
    print("\n📝 Conversation History:")
    history = agent_manager.get_history(5)
    print(history)
    
    # Show analytics data
    print("\n📊 Analytics Data:")
    analytics_response = await run_single_query(
        "Show comprehensive learning analytics",
        agent_type="tutor"
    )
    print(f"Analytics: {analytics_response[:300]}...")

async def integration_examples():
    """Demonstrate integration with external systems"""
    
    print("\n\n🔗 Integration Examples")
    print("=" * 35)
    
    # File system integration
    print("\n📁 File System Integration:")
    file_response = await run_single_query(
        "List files and show directory information",
        agent_type="technical"
    )
    print(f"File operations: {file_response[:200]}...")
    
    # Configuration integration
    print("\n⚙️ Configuration Integration:")
    config_response = await run_single_query(
        "Show current system configuration",
        agent_type="technical"
    )
    print(f"Configuration: {config_response[:200]}...")

async def main():
    """Main function to run advanced examples"""
    
    try:
        print("🚀 Advanced AI Agents Examples")
        print("=" * 50)
        print(f"🔧 Environment: {config.environment}")
        print(f"🧠 Model: {config.default_model}")
        print(f"📊 Analytics: {'Enabled' if config.enable_analytics else 'Disabled'}")
        
        # Run advanced example categories
        await multi_agent_workflow()
        await complex_tool_combinations()
        await adaptive_learning_demo()
        await performance_optimization_demo()
        await skill_assessment_demo()
        await tutorial_system_demo()
        await error_recovery_demo()
        await data_persistence_demo()
        await integration_examples()
        
        print("\n\n🎉 All advanced examples completed!")
        print("💡 These examples show the full potential of the AI agents system")
        print("🎓 Try implementing your own workflows based on these patterns")
        
    except Exception as e:
        print(f"\n❌ Error running advanced examples: {e}")
        logger.error(f"Advanced examples error: {e}")
        if config.debug:
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    # Ensure proper event loop for Windows
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # Run advanced examples
    asyncio.run(main())
