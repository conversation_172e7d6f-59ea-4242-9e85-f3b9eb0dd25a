# 🤝 Contributing to Enhanced AI Agents System

Thank you for your interest in contributing to the Enhanced AI Agents System! This project aims to provide an educational and comprehensive framework for learning and teaching AI agent development.

**Repository:** [https://github.com/asadullah48/tool.git](https://github.com/asadullah48/tool.git)
**Maintainer:** [asadull<PERSON><PERSON>](https://github.com/asadullah48)
**License:** MIT

**🎓 Educational Foundation:** [Panaversity - Learn Agentic AI](https://panaversity.org/)
**📚 Course Materials:** [AI Agents First](https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first)

## 🎯 Ways to Contribute

### 🐛 **Bug Reports**
- Report bugs via [GitHub Issues](https://github.com/asadullah48/tool/issues)
- Include detailed steps to reproduce
- Provide system information and error messages
- Check existing issues before creating new ones

### 💡 **Feature Requests**
- Suggest new features or improvements
- Explain the use case and benefits
- Consider educational value for learners
- Discuss implementation approaches

### 📝 **Documentation**
- Improve existing documentation
- Add new tutorials and guides
- Fix typos and clarify instructions
- Translate content to other languages

### 🛠️ **Code Contributions**
- Fix bugs and implement features
- Add new function tools and exercises
- Improve performance and reliability
- Enhance the practice interface

### 🎓 **Educational Content**
- Create new practice exercises
- Design learning paths and curricula
- Add teaching resources and lesson plans
- Develop assessment tools

## 🚀 Getting Started

### **1. Fork and Clone**
```bash
# Fork the repository on GitHub
# Then clone your fork
git clone https://github.com/YOUR_USERNAME/tool.git
cd tool
```

### **2. Set Up Development Environment**
```bash
# Install dependencies
uv add --dev pytest black flake8 mypy
# or
pip install -r requirements.txt pytest black flake8 mypy

# Set up pre-commit hooks (optional)
pip install pre-commit
pre-commit install
```

### **3. Create a Branch**
```bash
git checkout -b feature/your-feature-name
# or
git checkout -b fix/bug-description
```

### **4. Make Your Changes**
- Follow the coding standards below
- Add tests for new functionality
- Update documentation as needed
- Test your changes thoroughly

### **5. Submit a Pull Request**
```bash
git add .
git commit -m "Add: descriptive commit message"
git push origin feature/your-feature-name
```

Then create a pull request on GitHub with:
- Clear title and description
- Reference related issues
- Explain the changes made
- Include screenshots if applicable

## 📋 Development Guidelines

### **Code Style**
- Follow PEP 8 Python style guide
- Use meaningful variable and function names
- Add docstrings to all functions and classes
- Keep functions focused and modular
- Use type hints where appropriate

### **Example Code Style:**
```python
@function_tool("example_tool")
def example_tool(input_data: str, option: str = "default") -> str:
    """
    Example function tool with proper documentation.
    
    Args:
        input_data: Description of the input parameter
        option: Description of optional parameter
    
    Returns:
        Description of the return value
    
    Raises:
        ValueError: When input is invalid
    """
    if not input_data:
        raise ValueError("Input data cannot be empty")
    
    # Implementation here
    return f"Processed: {input_data} with {option}"
```

### **Testing**
- Write tests for all new functionality
- Ensure existing tests pass
- Aim for good test coverage
- Test edge cases and error conditions

### **Documentation**
- Update README.md for major changes
- Add docstrings to all functions
- Include usage examples
- Update practice guides as needed

## 🎓 Contributing Educational Content

### **Adding Practice Exercises**

1. **Choose Appropriate Level:**
   - Basic: Function tool fundamentals
   - Intermediate: Enhanced features and validation
   - Advanced: Complex integrations
   - Expert: AI coordination and workflows

2. **Exercise Structure:**
```python
{
    'id': 'unique_exercise_id',
    'title': 'Descriptive Exercise Title',
    'description': 'Brief description of what students will learn',
    'instruction': '''Detailed instructions for the exercise''',
    'template': '''Starting code template''',
    'solution': '''Complete working solution''',
    'test_cases': [
        {'input': 'test_input', 'expected_contains': ['expected', 'output']},
        # More test cases...
    ]
}
```

3. **Educational Guidelines:**
   - Start simple and build complexity
   - Include clear learning objectives
   - Provide helpful error messages
   - Add educational explanations
   - Consider different learning styles

### **Adding Agent Personalities**

1. **Define Personality:**
```python
"personality_name": {
    "name": "Display Name",
    "description": "Brief description",
    "instructions": "Detailed AI instructions",
    "specialties": ["area1", "area2"],
    "tone": "Communication style",
    "learning_focus": "Educational focus area"
}
```

2. **Consider:**
   - Educational value
   - Unique characteristics
   - Clear use cases
   - Appropriate tone

## 🧪 Testing Your Contributions

### **Run Tests**
```bash
# Run all tests
pytest

# Run specific test file
pytest tests/test_tools.py

# Run with coverage
pytest --cov=.
```

### **Code Quality Checks**
```bash
# Format code
black .

# Check style
flake8 .

# Type checking
mypy .
```

### **Manual Testing**
```bash
# Test main application
python main.py --demo

# Test practice interface
streamlit run practices.py

# Test specific functionality
python examples/basic_usage.py
```

## 📝 Commit Message Guidelines

Use clear, descriptive commit messages:

### **Format:**
```
Type: Brief description

Detailed explanation if needed
- Bullet points for multiple changes
- Reference issues with #123
```

### **Types:**
- `Add:` New features or content
- `Fix:` Bug fixes
- `Update:` Improvements to existing features
- `Docs:` Documentation changes
- `Test:` Adding or updating tests
- `Refactor:` Code restructuring
- `Style:` Formatting changes

### **Examples:**
```
Add: New data validation exercise for intermediate level

- Created comprehensive data validator tool exercise
- Added email, phone, and URL validation patterns
- Included test cases and solution
- Updated practice guide documentation

Fix: Streamlit session state initialization error

- Fixed KeyError when accessing uninitialized session state
- Added proper default values for all session variables
- Resolves #45
```

## 🔍 Review Process

### **Pull Request Review:**
1. **Automated Checks:** Code style, tests, and basic validation
2. **Manual Review:** Code quality, educational value, and functionality
3. **Testing:** Verify changes work as expected
4. **Documentation:** Ensure docs are updated appropriately
5. **Merge:** Approved changes are merged to main branch

### **Review Criteria:**
- ✅ Code follows style guidelines
- ✅ Tests pass and coverage is maintained
- ✅ Documentation is updated
- ✅ Educational value is clear
- ✅ No breaking changes (or properly documented)
- ✅ Performance impact is acceptable

## 🏆 Recognition

Contributors will be recognized in:
- README.md acknowledgments
- Release notes for significant contributions
- GitHub contributor statistics
- Special mentions for educational content

## 📞 Getting Help

### **Questions or Need Help?**
- 💬 **Discussions:** [GitHub Discussions](https://github.com/asadullah48/tool/discussions)
- 🐛 **Issues:** [GitHub Issues](https://github.com/asadullah48/tool/issues)
- 📧 **Direct Contact:** [asadullah48](https://github.com/asadullah48)

### **Resources:**
- 📚 [Practice Guide](PRACTICE_GUIDE.md)
- ⚡ [Quick Start](QUICKSTART.md)
- 🔧 [Examples](examples/)
- 📖 [Documentation](README.md)

## 📄 License

By contributing to this project, you agree that your contributions will be licensed under the MIT License.

---

**Thank you for helping make AI agent education better for everyone! 🚀🎓**
