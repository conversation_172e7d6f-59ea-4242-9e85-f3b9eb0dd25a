@echo off
echo Starting AI Agents Practice Lab...
echo.

REM Try different methods to run Streamlit
echo Attempting to run with UV...
uv run streamlit run practices.py
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo UV failed, trying direct Python...
    python -m streamlit run practices.py
    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo Direct Python failed, trying system Streamlit...
        streamlit run practices.py
        if %ERRORLEVEL% NEQ 0 (
            echo.
            echo All methods failed. Please check your installation.
            pause
        )
    )
)
