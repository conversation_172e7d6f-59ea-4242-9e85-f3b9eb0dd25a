# Install required packages
!pip install -Uq openai-agents nest-asyncio python-dotenv requests

# Import required libraries
import nest_asyncio
import os
import asyncio
import json
import random
from datetime import datetime
from typing import Dict, List, Optional

# Apply nest_asyncio for Jupyter compatibility
nest_asyncio.apply()

# Agent framework imports
from agents import Agent, Runner, AsyncOpenAI, OpenAIChatCompletionsModel, set_default_openai_client
from agents.run import RunConfig
from agents.tool import function_tool

# Environment handling
try:
    from google.colab import userdata
    IN_COLAB = True
except ImportError:
    IN_COLAB = False
    from dotenv import load_dotenv
    load_dotenv()

print("✅ All imports successful!")

# Configuration and API setup
def setup_gemini_client():
    """Setup Gemini API client with proper error handling"""
    
    # Get API key based on environment
    if IN_COLAB:
        try:
            gemini_api_key = userdata.get("GEMINI_API_KEY")
        except Exception as e:
            print(f"❌ Error accessing Colab secrets: {e}")
            gemini_api_key = None
    else:
        gemini_api_key = os.getenv("GEMINI_API_KEY")
    
    # Validate API key
    if not gemini_api_key:
        error_msg = (
            "GEMINI_API_KEY is not set. Please:\n"
            "- For Colab: Add it to Secrets in the left panel\n"
            "- For local: Set it in your .env file or environment variables"
        )
        raise ValueError(error_msg)
    
    # Create OpenAI-compatible client for Gemini
    external_client = AsyncOpenAI(
        api_key=gemini_api_key,
        base_url="https://generativelanguage.googleapis.com/v1beta/openai/",
    )
    
    # Set as default client
    set_default_openai_client(external_client)
    
    # Create model configuration
    model = OpenAIChatCompletionsModel(
        model="gemini-2.0-flash",
        openai_client=external_client
    )
    
    print("✅ Gemini API client configured successfully!")
    return model, external_client

# Setup the client
model, client = setup_gemini_client()

# Enhanced Weather Tool
@function_tool("get_weather")
def get_weather(location: str, unit: str = "C") -> str:
    """
    Fetch weather information for a given location.
    
    Args:
        location: The city or location name
        unit: Temperature unit ('C' for Celsius, 'F' for Fahrenheit)
    
    Returns:
        Weather description string
    """
    # Simulate realistic weather data
    weather_conditions = ["sunny", "cloudy", "rainy", "partly cloudy", "overcast"]
    condition = random.choice(weather_conditions)
    
    if unit.upper() == "F":
        temp = random.randint(32, 95)
        unit_symbol = "°F"
    else:
        temp = random.randint(0, 35)
        unit_symbol = "°C"
    
    humidity = random.randint(30, 90)
    wind_speed = random.randint(5, 25)
    
    return (
        f"Weather in {location}:\n"
        f"🌡️ Temperature: {temp}{unit_symbol}\n"
        f"☁️ Condition: {condition.title()}\n"
        f"💧 Humidity: {humidity}%\n"
        f"💨 Wind Speed: {wind_speed} km/h\n"
        f"📅 Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
    )

# Enhanced Student Finder Tool
@function_tool("piaic_student_finder")
def student_finder(student_roll: int) -> str:
    """
    Find PIAIC student information based on roll number.
    
    Args:
        student_roll: Student roll number
    
    Returns:
        Student information or "Not Found" message
    """
    students_data = {
        1: {
            "name": "Qasim Hassan",
            "program": "AI & Data Science",
            "batch": "Q4-2024",
            "status": "Active",
            "email": "<EMAIL>"
        },
        2: {
            "name": "Sir Zia Khan",
            "program": "Instructor - AI & Cloud",
            "batch": "Faculty",
            "status": "Faculty",
            "email": "<EMAIL>"
        },
        3: {
            "name": "Daniyal Nagori",
            "program": "Web3 & Metaverse",
            "batch": "Q4-2024",
            "status": "Active",
            "email": "<EMAIL>"
        },
        4: {
            "name": "Ahmed Ali",
            "program": "Cloud Computing",
            "batch": "Q3-2024",
            "status": "Graduated",
            "email": "<EMAIL>"
        },
        5: {
            "name": "Fatima Sheikh",
            "program": "AI & Data Science",
            "batch": "Q4-2024",
            "status": "Active",
            "email": "<EMAIL>"
        }
    }
    
    student = students_data.get(student_roll)
    if student:
        return (
            f"👨‍🎓 Student Found (Roll #{student_roll}):\n"
            f"📛 Name: {student['name']}\n"
            f"📚 Program: {student['program']}\n"
            f"🎯 Batch: {student['batch']}\n"
            f"✅ Status: {student['status']}\n"
            f"📧 Email: {student['email']}"
        )
    else:
        return f"❌ Student with roll number {student_roll} not found in PIAIC database."

# Calculator Tool
@function_tool("calculator")
def calculator(expression: str) -> str:
    """
    Perform mathematical calculations safely.
    
    Args:
        expression: Mathematical expression to evaluate (e.g., "2 + 3 * 4")
    
    Returns:
        Calculation result or error message
    """
    try:
        # Safe evaluation - only allow basic math operations
        allowed_chars = set('0123456789+-*/.() ')
        if not all(c in allowed_chars for c in expression):
            return "❌ Error: Only basic mathematical operations are allowed (+, -, *, /, parentheses)"
        
        result = eval(expression)
        return f"🧮 Calculation: {expression} = {result}"
    except ZeroDivisionError:
        return "❌ Error: Division by zero is not allowed"
    except Exception as e:
        return f"❌ Error: Invalid mathematical expression - {str(e)}"

# File Operations Tool
@function_tool("file_operations")
def file_operations(operation: str, filename: str, content: str = "") -> str:
    """
    Perform basic file operations (read, write, list).
    
    Args:
        operation: Type of operation ('read', 'write', 'list')
        filename: Name of the file
        content: Content to write (only for 'write' operation)
    
    Returns:
        Operation result or error message
    """
    try:
        if operation == "list":
            files = os.listdir(".")
            return f"📁 Files in current directory:\n" + "\n".join([f"📄 {f}" for f in files[:10]])
        
        elif operation == "read":
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()[:500]  # Limit to first 500 characters
                return f"📖 Content of {filename}:\n{content}{'...' if len(content) == 500 else ''}"
            else:
                return f"❌ File '{filename}' not found"
        
        elif operation == "write":
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            return f"✅ Successfully wrote content to '{filename}'"
        
        else:
            return "❌ Invalid operation. Use 'read', 'write', or 'list'"
    
    except Exception as e:
        return f"❌ File operation error: {str(e)}"

# Web Search Simulation Tool
@function_tool("web_search")
def web_search(query: str, max_results: int = 3) -> str:
    """
    Simulate web search results (for demonstration purposes).
    
    Args:
        query: Search query
        max_results: Maximum number of results to return
    
    Returns:
        Simulated search results
    """
    # Simulate search results based on query keywords
    search_results = {
        "python": [
            "🐍 Python.org - Official Python Website",
            "📚 Python Tutorial - Learn Python Programming",
            "🔧 Python Package Index (PyPI)"
        ],
        "ai": [
            "🤖 OpenAI - Artificial Intelligence Research",
            "🧠 Machine Learning Basics - Introduction to AI",
            "⚡ TensorFlow - Open Source ML Platform"
        ],
        "weather": [
            "🌤️ Weather.com - Current Weather Conditions",
            "📊 AccuWeather - Weather Forecasts",
            "🌍 Weather Underground - Local Weather"
        ]
    }
    
    # Find relevant results
    results = []
    query_lower = query.lower()
    
    for keyword, urls in search_results.items():
        if keyword in query_lower:
            results.extend(urls)
    
    if not results:
        results = [
            f"🔍 Search result for '{query}' - Example Website 1",
            f"📄 '{query}' Information - Example Website 2",
            f"💡 Learn about '{query}' - Example Website 3"
        ]
    
    # Limit results
    results = results[:max_results]
    
    return f"🔍 Search results for '{query}':\n" + "\n".join([f"{i+1}. {result}" for i, result in enumerate(results)])

print("✅ All tools defined successfully!")

# Create the AI Agent with enhanced capabilities
def create_enhanced_agent(model, personality="helpful"):
    """
    Create an AI agent with custom tools and personality.
    
    Args:
        model: The language model to use
        personality: Agent personality type
    
    Returns:
        Configured Agent instance
    """
    
    # Define different personality instructions
    personalities = {
        "helpful": (
            "You are a helpful and knowledgeable assistant. "
            "Provide clear, accurate, and detailed responses. "
            "Use the available tools when appropriate to give the best answers."
        ),
        "haiku": (
            "You are a poetic assistant who responds in haiku format. "
            "Each response should be exactly 3 lines with 5-7-5 syllable pattern. "
            "Use tools when needed, but present results poetically."
        ),
        "technical": (
            "You are a technical expert assistant. "
            "Provide detailed technical explanations with code examples when relevant. "
            "Use precise terminology and include implementation details."
        ),
        "friendly": (
            "You are a friendly and enthusiastic assistant! "
            "Use emojis and casual language to make interactions fun and engaging. "
            "Always be positive and encouraging in your responses."
        )
    }
    
    instructions = personalities.get(personality, personalities["helpful"])
    
    # Create agent with all tools
    agent = Agent(
        name="Enhanced AI Assistant",
        instructions=instructions,
        tools=[
            get_weather,
            student_finder,
            calculator,
            file_operations,
            web_search
        ],
        model=model
    )
    
    return agent

# Create different agent personalities
helpful_agent = create_enhanced_agent(model, "helpful")
haiku_agent = create_enhanced_agent(model, "haiku")
technical_agent = create_enhanced_agent(model, "technical")
friendly_agent = create_enhanced_agent(model, "friendly")

print("✅ AI Agents created successfully!")
print("Available agents: helpful_agent, haiku_agent, technical_agent, friendly_agent")

# Demo functions for testing the agents
async def run_demo_query(agent, query: str, demo_name: str = ""):
    """
    Run a demo query with an agent and display results nicely.
    
    Args:
        agent: The AI agent to use
        query: The query to ask
        demo_name: Name of the demo for display
    """
    print(f"\n{'='*60}")
    if demo_name:
        print(f"🎯 DEMO: {demo_name}")
    print(f"❓ Query: {query}")
    print(f"🤖 Agent: {agent.name}")
    print(f"{'='*60}")
    
    try:
        result = await Runner.run(agent, query)
        print(f"\n💬 Response:\n{result.final_output}")
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
    
    print(f"\n{'='*60}\n")

async def run_comprehensive_demo():
    """
    Run a comprehensive demo showcasing all tools and agent personalities.
    """
    print("🚀 Starting Comprehensive AI Agent Demo...\n")
    
    # Demo queries for different tools
    demos = [
        {
            "agent": helpful_agent,
            "query": "What's the weather like in Karachi?",
            "name": "Weather Tool Demo"
        },
        {
            "agent": helpful_agent,
            "query": "Find information about PIAIC student with roll number 2",
            "name": "Student Finder Demo"
        },
        {
            "agent": technical_agent,
            "query": "Calculate the result of (15 * 8) + (100 / 4) - 7",
            "name": "Calculator Tool Demo"
        },
        {
            "agent": friendly_agent,
            "query": "Search for information about Python programming",
            "name": "Web Search Demo"
        },
        {
            "agent": haiku_agent,
            "query": "Tell me about student roll number 1 and the weather in Tokyo",
            "name": "Multi-Tool Haiku Demo"
        },
        {
            "agent": helpful_agent,
            "query": "List the files in the current directory",
            "name": "File Operations Demo"
        }
    ]
    
    # Run all demos
    for demo in demos:
        await run_demo_query(demo["agent"], demo["query"], demo["name"])
        # Small delay between demos
        await asyncio.sleep(1)
    
    print("🎉 Comprehensive demo completed!")

async def interactive_chat(agent):
    """
    Start an interactive chat session with the agent.
    
    Args:
        agent: The AI agent to chat with
    """
    print(f"\n💬 Starting interactive chat with {agent.name}")
    print("Type 'quit' or 'exit' to end the conversation.\n")
    
    while True:
        try:
            user_input = input("You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("👋 Goodbye! Thanks for chatting!")
                break
            
            if not user_input:
                continue
            
            print(f"\n🤖 {agent.name}: ", end="")
            result = await Runner.run(agent, user_input)
            print(result.final_output)
            print()
            
        except KeyboardInterrupt:
            print("\n\n👋 Chat interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {str(e)}\n")

print("✅ Demo functions ready!")

# Quick demo - Test a single query
await run_demo_query(
    helpful_agent, 
    "Find student with roll number 1 and tell me the weather in Lahore",
    "Quick Multi-Tool Demo"
)

# Run the comprehensive demo (uncomment to run)
# await run_comprehensive_demo()

# Start interactive chat (uncomment to run)
# await interactive_chat(friendly_agent)

# Custom query example - modify as needed
custom_query = "Calculate 25 * 4 + 10 and then search for AI information"
await run_demo_query(technical_agent, custom_query, "Custom Query Demo")