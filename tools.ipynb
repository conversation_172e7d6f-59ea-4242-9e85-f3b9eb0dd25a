{"cells": [{"cell_type": "markdown", "id": "notebook-title", "metadata": {}, "source": ["# AI Agents with <PERSON>ls Demo\n", "\n", "This notebook demonstrates how to create AI agents with custom tools using the OpenAI Agents framework and Gemini API.\n", "\n", "## Features:\n", "- Weather information tool\n", "- Student finder tool\n", "- Calculator tool\n", "- File operations tool\n", "- Web search simulation\n", "\n", "## Setup Requirements:\n", "1. Install required packages\n", "2. Set up Gemini API key\n", "3. Configure the agent with tools"]}, {"cell_type": "code", "execution_count": null, "id": "setup-cell", "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install -Uq openai-agents nest-asyncio python-dotenv requests"]}, {"cell_type": "code", "execution_count": null, "id": "imports-cell", "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import nest_asyncio\n", "import os\n", "import asyncio\n", "import json\n", "import random\n", "from datetime import datetime\n", "from typing import Dict, List, Optional\n", "\n", "# Apply nest_asyncio for Jupyter compatibility\n", "nest_asyncio.apply()\n", "\n", "# Agent framework imports\n", "from agents import Agent, Runner, AsyncOpenAI, OpenAIChatCompletionsModel, set_default_openai_client\n", "from agents.run import RunConfig\n", "from agents.tool import function_tool\n", "\n", "# Environment handling\n", "try:\n", "    from google.colab import userdata\n", "    IN_COLAB = True\n", "except ImportError:\n", "    IN_COLAB = False\n", "    from dotenv import load_dotenv\n", "    load_dotenv()\n", "\n", "print(\"✅ All imports successful!\")"]}, {"cell_type": "code", "execution_count": null, "id": "config-cell", "metadata": {}, "outputs": [], "source": ["# Configuration and API setup\n", "def setup_gemini_client():\n", "    \"\"\"Setup Gemini API client with proper error handling\"\"\"\n", "    \n", "    # Get API key based on environment\n", "    if IN_COLAB:\n", "        try:\n", "            gemini_api_key = userdata.get(\"GEMINI_API_KEY\")\n", "        except Exception as e:\n", "            print(f\"❌ Error accessing Colab secrets: {e}\")\n", "            gemini_api_key = None\n", "    else:\n", "        gemini_api_key = os.getenv(\"GEMINI_API_KEY\")\n", "    \n", "    # Validate API key\n", "    if not gemini_api_key:\n", "        error_msg = (\n", "            \"GEMINI_API_KEY is not set. Please:\\n\"\n", "            \"- For Colab: Add it to <PERSON> in the left panel\\n\"\n", "            \"- For local: Set it in your .env file or environment variables\"\n", "        )\n", "        raise ValueError(error_msg)\n", "    \n", "    # Create OpenAI-compatible client for Gemini\n", "    external_client = AsyncOpenAI(\n", "        api_key=gemini_api_key,\n", "        base_url=\"https://generativelanguage.googleapis.com/v1beta/openai/\",\n", "    )\n", "    \n", "    # Set as default client\n", "    set_default_openai_client(external_client)\n", "    \n", "    # Create model configuration\n", "    model = OpenAIChatCompletionsModel(\n", "        model=\"gemini-2.0-flash\",\n", "        openai_client=external_client\n", "    )\n", "    \n", "    print(\"✅ Gemini API client configured successfully!\")\n", "    return model, external_client\n", "\n", "# Setup the client\n", "model, client = setup_gemini_client()"]}, {"cell_type": "markdown", "id": "tools-section", "metadata": {}, "source": ["## Custom Tools Definition\n", "\n", "Here we define various tools that our AI agent can use to perform different tasks."]}, {"cell_type": "code", "execution_count": null, "id": "tools-cell", "metadata": {}, "outputs": [], "source": ["# Enhanced Weather Tool\n", "@function_tool(\"get_weather\")\n", "def get_weather(location: str, unit: str = \"C\") -> str:\n", "    \"\"\"\n", "    Fetch weather information for a given location.\n", "    \n", "    Args:\n", "        location: The city or location name\n", "        unit: Temperature unit ('C' for Celsius, 'F' for Fahrenheit)\n", "    \n", "    Returns:\n", "        Weather description string\n", "    \"\"\"\n", "    # Simulate realistic weather data\n", "    weather_conditions = [\"sunny\", \"cloudy\", \"rainy\", \"partly cloudy\", \"overcast\"]\n", "    condition = random.choice(weather_conditions)\n", "    \n", "    if unit.upper() == \"F\":\n", "        temp = random.randint(32, 95)\n", "        unit_symbol = \"°F\"\n", "    else:\n", "        temp = random.randint(0, 35)\n", "        unit_symbol = \"°C\"\n", "    \n", "    humidity = random.randint(30, 90)\n", "    wind_speed = random.randint(5, 25)\n", "    \n", "    return (\n", "        f\"Weather in {location}:\\n\"\n", "        f\"🌡️ Temperature: {temp}{unit_symbol}\\n\"\n", "        f\"☁️ Condition: {condition.title()}\\n\"\n", "        f\"💧 Humidity: {humidity}%\\n\"\n", "        f\"💨 Wind Speed: {wind_speed} km/h\\n\"\n", "        f\"📅 Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M')}\"\n", "    )\n", "\n", "# Enhanced Student Finder <PERSON>l\n", "@function_tool(\"piaic_student_finder\")\n", "def student_finder(student_roll: int) -> str:\n", "    \"\"\"\n", "    Find PIAIC student information based on roll number.\n", "    \n", "    Args:\n", "        student_roll: Student roll number\n", "    \n", "    Returns:\n", "        Student information or \"Not Found\" message\n", "    \"\"\"\n", "    students_data = {\n", "        1: {\n", "            \"name\": \"<PERSON><PERSON><PERSON>\",\n", "            \"program\": \"AI & Data Science\",\n", "            \"batch\": \"Q4-2024\",\n", "            \"status\": \"Active\",\n", "            \"email\": \"<EMAIL>\"\n", "        },\n", "        2: {\n", "            \"name\": \"Sir <PERSON><PERSON>\",\n", "            \"program\": \"Instructor - AI & Cloud\",\n", "            \"batch\": \"Faculty\",\n", "            \"status\": \"Faculty\",\n", "            \"email\": \"<EMAIL>\"\n", "        },\n", "        3: {\n", "            \"name\": \"<PERSON><PERSON>\",\n", "            \"program\": \"Web3 & Metaverse\",\n", "            \"batch\": \"Q4-2024\",\n", "            \"status\": \"Active\",\n", "            \"email\": \"<EMAIL>\"\n", "        },\n", "        4: {\n", "            \"name\": \"<PERSON>\",\n", "            \"program\": \"Cloud Computing\",\n", "            \"batch\": \"Q3-2024\",\n", "            \"status\": \"Graduated\",\n", "            \"email\": \"<EMAIL>\"\n", "        },\n", "        5: {\n", "            \"name\": \"<PERSON><PERSON>\",\n", "            \"program\": \"AI & Data Science\",\n", "            \"batch\": \"Q4-2024\",\n", "            \"status\": \"Active\",\n", "            \"email\": \"<EMAIL>\"\n", "        }\n", "    }\n", "    \n", "    student = students_data.get(student_roll)\n", "    if student:\n", "        return (\n", "            f\"👨‍🎓 Student Found (Roll #{student_roll}):\\n\"\n", "            f\"📛 Name: {student['name']}\\n\"\n", "            f\"📚 Program: {student['program']}\\n\"\n", "            f\"🎯 Batch: {student['batch']}\\n\"\n", "            f\"✅ Status: {student['status']}\\n\"\n", "            f\"📧 Email: {student['email']}\"\n", "        )\n", "    else:\n", "        return f\"❌ Student with roll number {student_roll} not found in PIAIC database.\"\n", "\n", "# Calculator Tool\n", "@function_tool(\"calculator\")\n", "def calculator(expression: str) -> str:\n", "    \"\"\"\n", "    Perform mathematical calculations safely.\n", "    \n", "    Args:\n", "        expression: Mathematical expression to evaluate (e.g., \"2 + 3 * 4\")\n", "    \n", "    Returns:\n", "        Calculation result or error message\n", "    \"\"\"\n", "    try:\n", "        # Safe evaluation - only allow basic math operations\n", "        allowed_chars = set('0123456789+-*/.() ')\n", "        if not all(c in allowed_chars for c in expression):\n", "            return \"❌ Error: Only basic mathematical operations are allowed (+, -, *, /, parentheses)\"\n", "        \n", "        result = eval(expression)\n", "        return f\"🧮 Calculation: {expression} = {result}\"\n", "    except ZeroDivisionError:\n", "        return \"❌ Error: Division by zero is not allowed\"\n", "    except Exception as e:\n", "        return f\"❌ Error: Invalid mathematical expression - {str(e)}\"\n", "\n", "# File Operations Tool\n", "@function_tool(\"file_operations\")\n", "def file_operations(operation: str, filename: str, content: str = \"\") -> str:\n", "    \"\"\"\n", "    Perform basic file operations (read, write, list).\n", "    \n", "    Args:\n", "        operation: Type of operation ('read', 'write', 'list')\n", "        filename: Name of the file\n", "        content: Content to write (only for 'write' operation)\n", "    \n", "    Returns:\n", "        Operation result or error message\n", "    \"\"\"\n", "    try:\n", "        if operation == \"list\":\n", "            files = os.listdir(\".\")\n", "            return f\"📁 Files in current directory:\\n\" + \"\\n\".join([f\"📄 {f}\" for f in files[:10]])\n", "        \n", "        elif operation == \"read\":\n", "            if os.path.exists(filename):\n", "                with open(filename, 'r', encoding='utf-8') as f:\n", "                    content = f.read()[:500]  # Limit to first 500 characters\n", "                return f\"📖 Content of {filename}:\\n{content}{'...' if len(content) == 500 else ''}\"\n", "            else:\n", "                return f\"❌ File '{filename}' not found\"\n", "        \n", "        elif operation == \"write\":\n", "            with open(filename, 'w', encoding='utf-8') as f:\n", "                f.write(content)\n", "            return f\"✅ Successfully wrote content to '{filename}'\"\n", "        \n", "        else:\n", "            return \"❌ Invalid operation. Use 'read', 'write', or 'list'\"\n", "    \n", "    except Exception as e:\n", "        return f\"❌ File operation error: {str(e)}\"\n", "\n", "# Web Search Simulation Tool\n", "@function_tool(\"web_search\")\n", "def web_search(query: str, max_results: int = 3) -> str:\n", "    \"\"\"\n", "    Simulate web search results (for demonstration purposes).\n", "    \n", "    Args:\n", "        query: Search query\n", "        max_results: Maximum number of results to return\n", "    \n", "    Returns:\n", "        Simulated search results\n", "    \"\"\"\n", "    # Simulate search results based on query keywords\n", "    search_results = {\n", "        \"python\": [\n", "            \"🐍 Python.org - Official Python Website\",\n", "            \"📚 Python Tutorial - Learn Python Programming\",\n", "            \"🔧 Python Package Index (PyPI)\"\n", "        ],\n", "        \"ai\": [\n", "            \"🤖 OpenAI - Artificial Intelligence Research\",\n", "            \"🧠 Machine Learning Basics - Introduction to AI\",\n", "            \"⚡ TensorFlow - Open Source ML Platform\"\n", "        ],\n", "        \"weather\": [\n", "            \"🌤️ Weather.com - Current Weather Conditions\",\n", "            \"📊 AccuWeather - Weather Forecasts\",\n", "            \"🌍 Weather Underground - Local Weather\"\n", "        ]\n", "    }\n", "    \n", "    # Find relevant results\n", "    results = []\n", "    query_lower = query.lower()\n", "    \n", "    for keyword, urls in search_results.items():\n", "        if keyword in query_lower:\n", "            results.extend(urls)\n", "    \n", "    if not results:\n", "        results = [\n", "            f\"🔍 Search result for '{query}' - Example Website 1\",\n", "            f\"📄 '{query}' Information - Example Website 2\",\n", "            f\"💡 Learn about '{query}' - Example Website 3\"\n", "        ]\n", "    \n", "    # Limit results\n", "    results = results[:max_results]\n", "    \n", "    return f\"🔍 Search results for '{query}':\\n\" + \"\\n\".join([f\"{i+1}. {result}\" for i, result in enumerate(results)])\n", "\n", "print(\"✅ All tools defined successfully!\")"]}, {"cell_type": "markdown", "id": "agent-section", "metadata": {}, "source": ["## Agent Creation and Configuration\n", "\n", "Now we'll create our AI agent with all the tools we've defined."]}, {"cell_type": "code", "execution_count": null, "id": "agent-cell", "metadata": {}, "outputs": [], "source": ["# Create the AI Agent with enhanced capabilities\n", "def create_enhanced_agent(model, personality=\"helpful\"):\n", "    \"\"\"\n", "    Create an AI agent with custom tools and personality.\n", "    \n", "    Args:\n", "        model: The language model to use\n", "        personality: Agent personality type\n", "    \n", "    Returns:\n", "        Configured Agent instance\n", "    \"\"\"\n", "    \n", "    # Define different personality instructions\n", "    personalities = {\n", "        \"helpful\": (\n", "            \"You are a helpful and knowledgeable assistant. \"\n", "            \"Provide clear, accurate, and detailed responses. \"\n", "            \"Use the available tools when appropriate to give the best answers.\"\n", "        ),\n", "        \"haiku\": (\n", "            \"You are a poetic assistant who responds in haiku format. \"\n", "            \"Each response should be exactly 3 lines with 5-7-5 syllable pattern. \"\n", "            \"Use tools when needed, but present results poetically.\"\n", "        ),\n", "        \"technical\": (\n", "            \"You are a technical expert assistant. \"\n", "            \"Provide detailed technical explanations with code examples when relevant. \"\n", "            \"Use precise terminology and include implementation details.\"\n", "        ),\n", "        \"friendly\": (\n", "            \"You are a friendly and enthusiastic assistant! \"\n", "            \"Use emojis and casual language to make interactions fun and engaging. \"\n", "            \"Always be positive and encouraging in your responses.\"\n", "        )\n", "    }\n", "    \n", "    instructions = personalities.get(personality, personalities[\"helpful\"])\n", "    \n", "    # Create agent with all tools\n", "    agent = Agent(\n", "        name=\"Enhanced AI Assistant\",\n", "        instructions=instructions,\n", "        tools=[\n", "            get_weather,\n", "            student_finder,\n", "            calculator,\n", "            file_operations,\n", "            web_search\n", "        ],\n", "        model=model\n", "    )\n", "    \n", "    return agent\n", "\n", "# Create different agent personalities\n", "helpful_agent = create_enhanced_agent(model, \"helpful\")\n", "haiku_agent = create_enhanced_agent(model, \"haiku\")\n", "technical_agent = create_enhanced_agent(model, \"technical\")\n", "friendly_agent = create_enhanced_agent(model, \"friendly\")\n", "\n", "print(\"✅ AI Agents created successfully!\")\n", "print(\"Available agents: helpful_agent, haiku_agent, technical_agent, friendly_agent\")"]}, {"cell_type": "markdown", "id": "demo-section", "metadata": {}, "source": ["## Interactive Demo Functions\n", "\n", "Let's create some demo functions to test our agents with different queries."]}, {"cell_type": "code", "execution_count": null, "id": "demo-cell", "metadata": {}, "outputs": [], "source": ["# Demo functions for testing the agents\n", "async def run_demo_query(agent, query: str, demo_name: str = \"\"):\n", "    \"\"\"\n", "    Run a demo query with an agent and display results nicely.\n", "    \n", "    Args:\n", "        agent: The AI agent to use\n", "        query: The query to ask\n", "        demo_name: Name of the demo for display\n", "    \"\"\"\n", "    print(f\"\\n{'='*60}\")\n", "    if demo_name:\n", "        print(f\"🎯 DEMO: {demo_name}\")\n", "    print(f\"❓ Query: {query}\")\n", "    print(f\"🤖 Agent: {agent.name}\")\n", "    print(f\"{'='*60}\")\n", "    \n", "    try:\n", "        result = await Runner.run(agent, query)\n", "        print(f\"\\n💬 Response:\\n{result.final_output}\")\n", "    except Exception as e:\n", "        print(f\"\\n❌ Error: {str(e)}\")\n", "    \n", "    print(f\"\\n{'='*60}\\n\")\n", "\n", "async def run_comprehensive_demo():\n", "    \"\"\"\n", "    Run a comprehensive demo showcasing all tools and agent personalities.\n", "    \"\"\"\n", "    print(\"🚀 Starting Comprehensive AI Agent Demo...\\n\")\n", "    \n", "    # Demo queries for different tools\n", "    demos = [\n", "        {\n", "            \"agent\": helpful_agent,\n", "            \"query\": \"What's the weather like in Karachi?\",\n", "            \"name\": \"Weather Tool Demo\"\n", "        },\n", "        {\n", "            \"agent\": helpful_agent,\n", "            \"query\": \"Find information about PIAIC student with roll number 2\",\n", "            \"name\": \"Student Finder De<PERSON>\"\n", "        },\n", "        {\n", "            \"agent\": technical_agent,\n", "            \"query\": \"Calculate the result of (15 * 8) + (100 / 4) - 7\",\n", "            \"name\": \"Calculator Tool Demo\"\n", "        },\n", "        {\n", "            \"agent\": friendly_agent,\n", "            \"query\": \"Search for information about Python programming\",\n", "            \"name\": \"Web Search Demo\"\n", "        },\n", "        {\n", "            \"agent\": haiku_agent,\n", "            \"query\": \"Tell me about student roll number 1 and the weather in Tokyo\",\n", "            \"name\": \"Multi-Tool Haiku Demo\"\n", "        },\n", "        {\n", "            \"agent\": helpful_agent,\n", "            \"query\": \"List the files in the current directory\",\n", "            \"name\": \"File Operations Demo\"\n", "        }\n", "    ]\n", "    \n", "    # Run all demos\n", "    for demo in demos:\n", "        await run_demo_query(demo[\"agent\"], demo[\"query\"], demo[\"name\"])\n", "        # Small delay between demos\n", "        await asyncio.sleep(1)\n", "    \n", "    print(\"🎉 Comprehensive demo completed!\")\n", "\n", "async def interactive_chat(agent):\n", "    \"\"\"\n", "    Start an interactive chat session with the agent.\n", "    \n", "    Args:\n", "        agent: The AI agent to chat with\n", "    \"\"\"\n", "    print(f\"\\n💬 Starting interactive chat with {agent.name}\")\n", "    print(\"Type 'quit' or 'exit' to end the conversation.\\n\")\n", "    \n", "    while True:\n", "        try:\n", "            user_input = input(\"You: \").strip()\n", "            \n", "            if user_input.lower() in ['quit', 'exit', 'bye']:\n", "                print(\"👋 Goodbye! Thanks for chatting!\")\n", "                break\n", "            \n", "            if not user_input:\n", "                continue\n", "            \n", "            print(f\"\\n🤖 {agent.name}: \", end=\"\")\n", "            result = await Runner.run(agent, user_input)\n", "            print(result.final_output)\n", "            print()\n", "            \n", "        except KeyboardInterrupt:\n", "            print(\"\\n\\n👋 Cha<PERSON> interrupted. Goodbye!\")\n", "            break\n", "        except Exception as e:\n", "            print(f\"\\n❌ Error: {str(e)}\\n\")\n", "\n", "print(\"✅ De<PERSON> functions ready!\")"]}, {"cell_type": "markdown", "id": "usage-section", "metadata": {}, "source": ["## Usage Examples\n", "\n", "Now you can run the demos and interact with the agents!"]}, {"cell_type": "code", "execution_count": null, "id": "quick-demo-cell", "metadata": {}, "outputs": [], "source": ["# Quick demo - Test a single query\n", "await run_demo_query(\n", "    helpful_agent, \n", "    \"Find student with roll number 1 and tell me the weather in Lahore\",\n", "    \"Quick Multi-Tool Demo\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "comprehensive-demo-cell", "metadata": {}, "outputs": [], "source": ["# Run the comprehensive demo (uncomment to run)\n", "# await run_comprehensive_demo()"]}, {"cell_type": "code", "execution_count": null, "id": "interactive-chat-cell", "metadata": {}, "outputs": [], "source": ["# Start interactive chat (uncomment to run)\n", "# await interactive_chat(friendly_agent)"]}, {"cell_type": "code", "execution_count": null, "id": "custom-query-cell", "metadata": {}, "outputs": [], "source": ["# Custom query example - modify as needed\n", "custom_query = \"Calculate 25 * 4 + 10 and then search for AI information\"\n", "await run_demo_query(technical_agent, custom_query, \"Custom Query Demo\")"]}, {"cell_type": "markdown", "id": "conclusion-section", "metadata": {}, "source": ["## Conclusion\n", "\n", "This enhanced notebook demonstrates:\n", "\n", "### ✅ **Improvements Made:**\n", "1. **Better Structure**: Organized into clear sections with markdown documentation\n", "2. **Error Handling**: Robust error handling for API setup and tool operations\n", "3. **Multiple Tools**: 5 different tools (weather, student finder, calculator, file ops, web search)\n", "4. **Agent Personalities**: 4 different agent personalities (helpful, haiku, technical, friendly)\n", "5. **Environment Flexibility**: Works in both Colab and local environments\n", "6. **Interactive Features**: Demo functions and interactive chat capability\n", "7. **Enhanced Data**: More realistic and detailed tool responses\n", "8. **Safety**: Safe evaluation for calculator and file operations\n", "\n", "### 🚀 **Next Steps:**\n", "- Add more sophisticated tools (API integrations, database connections)\n", "- Implement conversation memory\n", "- Add tool chaining capabilities\n", "- Create custom tool templates\n", "- Add logging and analytics\n", "\n", "### 🛠️ **Usage Tips:**\n", "1. Make sure to set your `GEMINI_API_KEY` before running\n", "2. Uncomment demo cells to run different examples\n", "3. Modify the `custom_query` cell for your own tests\n", "4. Try different agent personalities for varied responses\n", "5. Use the interactive chat for real-time conversations\n", "\n", "**Happy coding with AI Agents! 🤖✨**"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}