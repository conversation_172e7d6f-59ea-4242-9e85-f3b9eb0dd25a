"""
Basic Usage Examples
===================

This script demonstrates basic usage of the AI agents system.
Run this script to see how to interact with agents programmatically.

Usage:
    python examples/basic_usage.py
"""

import sys
import asyncio
from pathlib import Path

# Add parent directory to path to import modules
sys.path.append(str(Path(__file__).parent.parent))

from config import config, logger
from main import agent_manager, run_single_query

async def basic_examples():
    """Run basic usage examples"""
    
    print("🚀 Basic AI Agents Usage Examples")
    print("=" * 50)
    
    # Example 1: Simple calculation
    print("\n📊 Example 1: Basic Calculation")
    print("-" * 30)
    response = await run_single_query(
        "Calculate 25 * 4 + 15 - 8",
        agent_type="helpful"
    )
    print(f"Response: {response}")
    
    # Example 2: Weather information
    print("\n🌤️ Example 2: Weather Information")
    print("-" * 35)
    response = await run_single_query(
        "What's the weather like in Karachi?",
        agent_type="helpful"
    )
    print(f"Response: {response}")
    
    # Example 3: Student database search
    print("\n👨‍🎓 Example 3: Student Database")
    print("-" * 32)
    response = await run_single_query(
        "Find student with roll number 3",
        agent_type="teacher"
    )
    print(f"Response: {response}")
    
    # Example 4: Different agent personalities
    print("\n🎭 Example 4: Agent Personalities")
    print("-" * 35)
    
    query = "Explain what AI agents are"
    
    # Technical explanation
    print("\n🔧 Technical Agent:")
    technical_response = await run_single_query(query, "technical")
    print(f"Response: {technical_response[:200]}...")
    
    # Friendly explanation
    print("\n😊 Friendly Agent:")
    friendly_response = await run_single_query(query, "friendly")
    print(f"Response: {friendly_response[:200]}...")
    
    # Teacher explanation
    print("\n👩‍🏫 Teacher Agent:")
    teacher_response = await run_single_query(query, "teacher")
    print(f"Response: {teacher_response[:200]}...")

async def tool_examples():
    """Demonstrate different tools"""
    
    print("\n\n🛠️ Tool Usage Examples")
    print("=" * 40)
    
    # Advanced calculator
    print("\n🧮 Advanced Calculator:")
    response = await run_single_query(
        "Calculate sqrt(144) + sin(30) * pi",
        agent_type="technical"
    )
    print(f"Response: {response}")
    
    # Data analysis
    print("\n📊 Data Analysis:")
    response = await run_single_query(
        "Analyze this data: [10, 15, 20, 25, 30, 35, 40]",
        agent_type="analyst"
    )
    print(f"Response: {response}")
    
    # Text processing
    print("\n📝 Text Processing:")
    response = await run_single_query(
        "Analyze this text: 'AI agents are transforming how we work with technology'",
        agent_type="analyst"
    )
    print(f"Response: {response}")
    
    # Web search simulation
    print("\n🔍 Web Search:")
    response = await run_single_query(
        "Search for information about Python programming",
        agent_type="researcher"
    )
    print(f"Response: {response}")

async def learning_examples():
    """Demonstrate learning features"""
    
    print("\n\n📚 Learning Features Examples")
    print("=" * 45)
    
    # Learning analytics
    print("\n📊 Learning Analytics:")
    response = await run_single_query(
        "Show me my learning analytics",
        agent_type="tutor"
    )
    print(f"Response: {response}")
    
    # System information
    print("\n💻 System Information:")
    response = await run_single_query(
        "Show me basic system information",
        agent_type="technical"
    )
    print(f"Response: {response}")

async def creative_examples():
    """Demonstrate creative applications"""
    
    print("\n\n🎨 Creative Applications")
    print("=" * 35)
    
    # Creative brainstorming
    print("\n💡 Creative Brainstorming:")
    response = await run_single_query(
        "Brainstorm creative uses for a calculator in education",
        agent_type="creative"
    )
    print(f"Response: {response}")
    
    # Research combination
    print("\n🔬 Research Combination:")
    response = await run_single_query(
        "Research data science and provide learning recommendations",
        agent_type="researcher"
    )
    print(f"Response: {response}")

async def error_handling_examples():
    """Demonstrate error handling"""
    
    print("\n\n⚠️ Error Handling Examples")
    print("=" * 40)
    
    # Invalid calculation
    print("\n❌ Invalid Calculation:")
    response = await run_single_query(
        "Calculate invalid_expression + unknown_function()",
        agent_type="helpful"
    )
    print(f"Response: {response}")
    
    # Non-existent student
    print("\n❌ Non-existent Student:")
    response = await run_single_query(
        "Find student with roll number 999",
        agent_type="teacher"
    )
    print(f"Response: {response}")

async def performance_examples():
    """Demonstrate performance monitoring"""
    
    print("\n\n⚡ Performance Examples")
    print("=" * 35)
    
    # Multiple operations for performance tracking
    operations = [
        ("Calculate 100 * 50", "helpful"),
        ("What's the weather in Lahore?", "helpful"),
        ("Find student 1", "teacher"),
        ("Analyze data: [1,2,3,4,5]", "analyst")
    ]
    
    print("\n🏃‍♂️ Running multiple operations...")
    for query, agent_type in operations:
        start_time = asyncio.get_event_loop().time()
        response = await run_single_query(query, agent_type)
        end_time = asyncio.get_event_loop().time()
        duration = end_time - start_time
        print(f"✅ {query[:30]}... completed in {duration:.2f}s")
    
    # Show analytics
    print("\n📊 Performance Analytics:")
    response = await run_single_query(
        "Show detailed learning analytics",
        agent_type="tutor"
    )
    print(f"Response: {response}")

async def main():
    """Main function to run all examples"""
    
    try:
        print("🎯 Starting AI Agents Examples")
        print("🔧 Configuration loaded successfully")
        print(f"🤖 Available agents: {len(agent_manager.agents)}")
        print(f"🛠️ Current model: {config.default_model}")
        
        # Run all example categories
        await basic_examples()
        await tool_examples()
        await learning_examples()
        await creative_examples()
        await error_handling_examples()
        await performance_examples()
        
        print("\n\n🎉 All examples completed successfully!")
        print("💡 Try running the interactive mode: python main.py")
        print("📚 Or start with tutorials: python main.py --tutorial")
        
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        logger.error(f"Examples error: {e}")
        if config.debug:
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    # Ensure proper event loop for Windows
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # Run examples
    asyncio.run(main())
