#!/usr/bin/env python3
"""
Simple Tool Function Learning Script
===================================

Learn how AI agent tools work step by step.

Repository: https://github.com/asadullah48/tool.git
Author: asadullah48
License: MIT

Learning Source: Panaversity - Learn Agentic AI
Course: https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first
Website: https://panaversity.org/
"""

import os
import sys
from datetime import datetime

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f"🎓 {title}")
    print("=" * 60)

def test_basic_tools():
    """Test basic tool functions"""
    
    print_header("BASIC TOOL FUNCTIONS LEARNING")
    
    print("\n1. 🕐 TIME TOOL")
    print("-" * 30)
    current_time = datetime.now()
    print(f"Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Timestamp: {current_time.timestamp()}")
    print("✅ Time tool works!")
    
    print("\n2. 🧮 CALCULATOR TOOL")
    print("-" * 30)
    def calculate(a, b, operation):
        """Simple calculator tool"""
        if operation == "add":
            return a + b
        elif operation == "subtract":
            return a - b
        elif operation == "multiply":
            return a * b
        elif operation == "divide":
            return a / b if b != 0 else "Error: Division by zero"
        else:
            return "Error: Unknown operation"
    
    # Test calculations
    test_cases = [
        (10, 5, "add"),
        (10, 5, "subtract"),
        (10, 5, "multiply"),
        (10, 5, "divide")
    ]
    
    for a, b, op in test_cases:
        result = calculate(a, b, op)
        print(f"{a} {op} {b} = {result}")
    
    print("✅ Calculator tool works!")
    
    print("\n3. 📝 TEXT PROCESSOR TOOL")
    print("-" * 30)
    def process_text(text, operation):
        """Text processing tool"""
        if operation == "uppercase":
            return text.upper()
        elif operation == "lowercase":
            return text.lower()
        elif operation == "word_count":
            return len(text.split())
        elif operation == "char_count":
            return len(text)
        elif operation == "reverse":
            return text[::-1]
        else:
            return "Error: Unknown operation"
    
    sample_text = "Hello from Panaversity AI Agents Course!"
    operations = ["uppercase", "lowercase", "word_count", "char_count", "reverse"]
    
    for op in operations:
        result = process_text(sample_text, op)
        print(f"{op}: {result}")
    
    print("✅ Text processor tool works!")
    
    print("\n4. 🌍 ENVIRONMENT INFO TOOL")
    print("-" * 30)
    print(f"Python version: {sys.version}")
    print(f"Platform: {sys.platform}")
    print(f"Current directory: {os.getcwd()}")
    print(f"Environment variables count: {len(os.environ)}")
    print("✅ Environment info tool works!")

def explain_tool_concepts():
    """Explain key tool concepts"""
    
    print_header("TOOL FUNCTION CONCEPTS")
    
    concepts = [
        {
            "concept": "🔧 What is a Tool Function?",
            "explanation": "A tool function is a piece of code that performs a specific task, like getting weather data, calculating math, or processing text."
        },
        {
            "concept": "🤖 How AI Agents Use Tools?",
            "explanation": "AI agents can call these tool functions to help users accomplish tasks. The agent decides which tool to use based on the user's request."
        },
        {
            "concept": "📡 API Integration",
            "explanation": "Many tools connect to external APIs (like weather services) to get real-time data and provide useful information."
        },
        {
            "concept": "🛡️ Error Handling",
            "explanation": "Good tools handle errors gracefully and provide meaningful feedback when something goes wrong."
        },
        {
            "concept": "🎯 Function Decorators",
            "explanation": "In AI agent frameworks, tools are often decorated with @function_tool to make them available to the AI agent."
        }
    ]
    
    for i, item in enumerate(concepts, 1):
        print(f"\n{i}. {item['concept']}")
        print(f"   {item['explanation']}")
    
    print("\n🎓 NEXT STEPS:")
    print("-" * 30)
    print("1. Run the main AI agent system: python main.py")
    print("2. Try the Streamlit interface: streamlit run practices.py")
    print("3. Explore tools_collection.py for more advanced tools")
    print("4. Learn about agent personalities and configurations")

if __name__ == "__main__":
    print("🚀 AI AGENTS TOOL FUNCTIONS LEARNING")
    print("Built with concepts from Panaversity's Learn Agentic AI course")
    print("🌐 https://panaversity.org/")
    print("📚 https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first")
    
    test_basic_tools()
    explain_tool_concepts()
    
    print("\n" + "=" * 60)
    print("🎉 Tool Functions Learning Complete!")
    print("=" * 60)
