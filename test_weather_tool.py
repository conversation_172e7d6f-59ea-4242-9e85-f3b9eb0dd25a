#!/usr/bin/env python3
"""
Weather Tool Testing Script
===========================

This script demonstrates how to use the weather API tool function.
Learn how tools work by running this step by step.

Repository: https://github.com/asadullah48/tool.git
Author: asadullah48
License: MIT

Learning Source: Panaversity - Learn Agentic AI
Course: https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first
Website: https://panaversity.org/
"""

import os
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_weather_api():
    """Test the weather API directly to understand how tools work"""
    
    print("🌤️  WEATHER TOOL FUNCTION LEARNING")
    print("=" * 50)
    print()
    
    # Get API key from environment
    api_key = os.getenv('WEATHER_API_KEY')
    if not api_key:
        print("❌ Weather API key not found in .env file")
        return
    
    print(f"✅ Weather API Key loaded: {api_key[:10]}...")
    print()
    
    # Test different cities
    cities = ["London", "New York", "Tokyo", "Karachi"]
    
    for city in cities:
        print(f"🏙️  Getting weather for {city}...")
        
        try:
            # Make API request
            url = f"https://api.openweathermap.org/data/2.5/weather"
            params = {
                'q': city,
                'appid': api_key,
                'units': 'metric'
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                # Extract weather information
                temp = data['main']['temp']
                feels_like = data['main']['feels_like']
                humidity = data['main']['humidity']
                description = data['weather'][0]['description']
                
                print(f"   🌡️  Temperature: {temp}°C (feels like {feels_like}°C)")
                print(f"   💧 Humidity: {humidity}%")
                print(f"   ☁️  Conditions: {description.title()}")
                print(f"   ✅ Success!")
                
            else:
                print(f"   ❌ Error: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
        
        print()
    
    print("🎓 LEARNING POINTS:")
    print("=" * 50)
    print("1. Tools are functions that interact with external APIs")
    print("2. They need API keys for authentication")
    print("3. They handle errors gracefully")
    print("4. They return structured data")
    print("5. AI agents can use these tools to help users")
    print()

if __name__ == "__main__":
    test_weather_api()
