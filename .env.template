# AI Agents Configuration File
# Copy this file to .env and fill in your actual values

# =============================================================================
# API KEYS
# =============================================================================

# Gemini API Key (Required)
# Get your API key from: https://ai.google.dev/
GEMINI_API_KEY=your_gemini_api_key_here

# OpenAI API Key (Optional - for comparison testing)
OPENAI_API_KEY=your_openai_api_key_here

# =============================================================================
# MODEL CONFIGURATION
# =============================================================================

# Default model to use
DEFAULT_MODEL=gemini-2.0-flash

# Alternative models (uncomment to use)
# DEFAULT_MODEL=gpt-4
# DEFAULT_MODEL=gpt-3.5-turbo

# Model provider base URLs
GEMINI_BASE_URL=https://generativelanguage.googleapis.com/v1beta/openai/
OPENAI_BASE_URL=https://api.openai.com/v1

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Environment (development, production, testing)
ENVIRONMENT=development

# Debug mode (true/false)
DEBUG=true

# Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Maximum conversation history to keep
MAX_CONVERSATION_HISTORY=50

# =============================================================================
# TOOL CONFIGURATIONS
# =============================================================================

# Weather API settings (if using real weather API)
WEATHER_API_KEY=your_weather_api_key_here
WEATHER_API_URL=https://api.openweathermap.org/data/2.5

# File operations settings
ALLOW_FILE_OPERATIONS=true
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_EXTENSIONS=.txt,.json,.csv,.md,.py

# Web search settings (if using real search API)
SEARCH_API_KEY=your_search_api_key_here
SEARCH_ENGINE_ID=your_search_engine_id_here

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Rate limiting
MAX_REQUESTS_PER_MINUTE=60
MAX_TOKENS_PER_REQUEST=4000

# Allowed operations
ALLOW_CODE_EXECUTION=false
ALLOW_SYSTEM_COMMANDS=false

# =============================================================================
# DATABASE SETTINGS (Optional)
# =============================================================================

# SQLite database path
DATABASE_PATH=./data/agents.db

# Enable conversation logging
ENABLE_CONVERSATION_LOGGING=true

# =============================================================================
# LEARNING FEATURES
# =============================================================================

# Enable analytics and learning features
ENABLE_ANALYTICS=true
ENABLE_TOOL_USAGE_TRACKING=true
ENABLE_PERFORMANCE_MONITORING=true

# Tutorial mode
ENABLE_TUTORIAL_MODE=true
SHOW_TOOL_EXPLANATIONS=true

# =============================================================================
# ADVANCED SETTINGS
# =============================================================================

# Request timeout in seconds
REQUEST_TIMEOUT=30

# Retry attempts for failed requests
MAX_RETRY_ATTEMPTS=3

# Enable experimental features
ENABLE_EXPERIMENTAL_FEATURES=false

# Custom agent personalities directory
CUSTOM_PERSONALITIES_DIR=./personalities

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Enable development tools
ENABLE_DEV_TOOLS=true

# Mock external APIs for testing
MOCK_EXTERNAL_APIS=false

# Verbose logging for debugging
VERBOSE_LOGGING=false
