#!/usr/bin/env python3
"""
Complete AI Agent Tools Function Demo
====================================

A comprehensive demonstration of how AI agent tools work, built with concepts
from Panaversity's Learn Agentic AI course.

Repository: https://github.com/asadullah48/tool.git
Author: asadullah48
License: MIT

Learning Source: Panaversity - Learn Agentic AI
Course: https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first
Website: https://panaversity.org/
"""

import os
import json
import requests
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class ToolsDemo:
    """Demonstration of AI Agent Tools Functions"""
    
    def __init__(self):
        self.weather_api_key = os.getenv('WEATHER_API_KEY')
        self.demo_results = []
    
    def print_header(self, title, emoji="🔧"):
        """Print formatted header"""
        print(f"\n{emoji} {title}")
        print("=" * (len(title) + 4))
    
    def print_subheader(self, title, emoji="📋"):
        """Print formatted subheader"""
        print(f"\n{emoji} {title}")
        print("-" * (len(title) + 4))
    
    def log_result(self, tool_name, success, details):
        """Log tool execution result"""
        self.demo_results.append({
            'tool': tool_name,
            'success': success,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
    
    def weather_tool(self, city="London"):
        """Weather API tool demonstration"""
        self.print_subheader("Weather Tool Function", "🌤️")
        
        if not self.weather_api_key:
            print("❌ Weather API key not configured")
            self.log_result("weather_tool", False, "API key missing")
            return None
        
        try:
            print(f"🏙️ Getting weather for {city}...")
            
            url = "https://api.openweathermap.org/data/2.5/weather"
            params = {
                'q': city,
                'appid': self.weather_api_key,
                'units': 'metric'
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                weather_info = {
                    'city': data['name'],
                    'country': data['sys']['country'],
                    'temperature': data['main']['temp'],
                    'feels_like': data['main']['feels_like'],
                    'humidity': data['main']['humidity'],
                    'description': data['weather'][0]['description'],
                    'wind_speed': data['wind']['speed']
                }
                
                print(f"   🌍 Location: {weather_info['city']}, {weather_info['country']}")
                print(f"   🌡️ Temperature: {weather_info['temperature']}°C (feels like {weather_info['feels_like']}°C)")
                print(f"   💧 Humidity: {weather_info['humidity']}%")
                print(f"   ☁️ Conditions: {weather_info['description'].title()}")
                print(f"   💨 Wind Speed: {weather_info['wind_speed']} m/s")
                print("   ✅ Weather tool executed successfully!")
                
                self.log_result("weather_tool", True, weather_info)
                return weather_info
            else:
                error_msg = f"API Error: {response.status_code}"
                print(f"   ❌ {error_msg}")
                self.log_result("weather_tool", False, error_msg)
                return None
                
        except Exception as e:
            error_msg = f"Exception: {str(e)}"
            print(f"   ❌ {error_msg}")
            self.log_result("weather_tool", False, error_msg)
            return None
    
    def calculator_tool(self, expression):
        """Calculator tool demonstration"""
        self.print_subheader("Calculator Tool Function", "🧮")
        
        try:
            print(f"🔢 Calculating: {expression}")
            
            # Safe evaluation (in real implementation, use proper math parser)
            allowed_chars = set('0123456789+-*/.() ')
            if all(c in allowed_chars for c in expression):
                result = eval(expression)
                print(f"   📊 Result: {expression} = {result}")
                print("   ✅ Calculator tool executed successfully!")
                
                calc_info = {'expression': expression, 'result': result}
                self.log_result("calculator_tool", True, calc_info)
                return result
            else:
                error_msg = "Invalid characters in expression"
                print(f"   ❌ {error_msg}")
                self.log_result("calculator_tool", False, error_msg)
                return None
                
        except Exception as e:
            error_msg = f"Calculation error: {str(e)}"
            print(f"   ❌ {error_msg}")
            self.log_result("calculator_tool", False, error_msg)
            return None
    
    def text_analyzer_tool(self, text):
        """Text analysis tool demonstration"""
        self.print_subheader("Text Analyzer Tool Function", "📝")
        
        try:
            print(f"📄 Analyzing text: '{text[:50]}{'...' if len(text) > 50 else ''}'")
            
            analysis = {
                'character_count': len(text),
                'word_count': len(text.split()),
                'sentence_count': text.count('.') + text.count('!') + text.count('?'),
                'uppercase_count': sum(1 for c in text if c.isupper()),
                'lowercase_count': sum(1 for c in text if c.islower()),
                'digit_count': sum(1 for c in text if c.isdigit()),
                'most_common_word': max(text.lower().split(), key=text.lower().split().count) if text.split() else None
            }
            
            print(f"   📊 Characters: {analysis['character_count']}")
            print(f"   📊 Words: {analysis['word_count']}")
            print(f"   📊 Sentences: {analysis['sentence_count']}")
            print(f"   📊 Uppercase letters: {analysis['uppercase_count']}")
            print(f"   📊 Lowercase letters: {analysis['lowercase_count']}")
            print(f"   📊 Digits: {analysis['digit_count']}")
            if analysis['most_common_word']:
                print(f"   📊 Most common word: '{analysis['most_common_word']}'")
            print("   ✅ Text analyzer tool executed successfully!")
            
            self.log_result("text_analyzer_tool", True, analysis)
            return analysis
            
        except Exception as e:
            error_msg = f"Analysis error: {str(e)}"
            print(f"   ❌ {error_msg}")
            self.log_result("text_analyzer_tool", False, error_msg)
            return None
    
    def time_tool(self):
        """Time and date tool demonstration"""
        self.print_subheader("Time Tool Function", "🕐")
        
        try:
            now = datetime.now()
            
            time_info = {
                'current_time': now.strftime('%Y-%m-%d %H:%M:%S'),
                'timestamp': now.timestamp(),
                'day_of_week': now.strftime('%A'),
                'month': now.strftime('%B'),
                'year': now.year,
                'hour': now.hour,
                'minute': now.minute,
                'second': now.second
            }
            
            print(f"   📅 Current Date & Time: {time_info['current_time']}")
            print(f"   📅 Day of Week: {time_info['day_of_week']}")
            print(f"   📅 Month: {time_info['month']}")
            print(f"   📅 Timestamp: {time_info['timestamp']}")
            print("   ✅ Time tool executed successfully!")
            
            self.log_result("time_tool", True, time_info)
            return time_info
            
        except Exception as e:
            error_msg = f"Time error: {str(e)}"
            print(f"   ❌ {error_msg}")
            self.log_result("time_tool", False, error_msg)
            return None
    
    def run_complete_demo(self):
        """Run complete tools demonstration"""
        
        self.print_header("AI AGENT TOOLS FUNCTION DEMONSTRATION", "🚀")
        print("Built with concepts from Panaversity's Learn Agentic AI course")
        print("🌐 https://panaversity.org/")
        print("📚 https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first")
        
        # Run all tool demonstrations
        self.time_tool()
        self.calculator_tool("(10 + 5) * 3 - 2")
        self.text_analyzer_tool("Hello from Panaversity AI Agents Course! This is a sample text for analysis.")
        self.weather_tool("London")
        self.weather_tool("New York")
        
        # Show summary
        self.print_header("DEMONSTRATION SUMMARY", "📊")
        
        successful_tools = sum(1 for result in self.demo_results if result['success'])
        total_tools = len(self.demo_results)
        
        print(f"✅ Successful tool executions: {successful_tools}/{total_tools}")
        print(f"📈 Success rate: {(successful_tools/total_tools)*100:.1f}%")
        
        print("\n📋 Tool Execution Log:")
        for i, result in enumerate(self.demo_results, 1):
            status = "✅" if result['success'] else "❌"
            print(f"   {i}. {result['tool']}: {status}")
        
        self.print_header("KEY LEARNING POINTS", "🎓")
        
        learning_points = [
            "🔧 Tools are functions that perform specific tasks",
            "🤖 AI agents use tools to help users accomplish goals",
            "📡 Tools can integrate with external APIs (like weather services)",
            "🛡️ Proper error handling is essential for robust tools",
            "📊 Tools return structured data that agents can interpret",
            "🔑 API keys and configuration are crucial for external services",
            "⚡ Tools should be fast, reliable, and user-friendly"
        ]
        
        for point in learning_points:
            print(f"   {point}")
        
        print(f"\n🎉 Tools demonstration complete! Ready to build AI agents!")

if __name__ == "__main__":
    demo = ToolsDemo()
    demo.run_complete_demo()
