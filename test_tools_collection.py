#!/usr/bin/env python3
"""
Advanced Tools Collection Testing
=================================

Test the actual tools from tools_collection.py to understand how they work.

Repository: https://github.com/asadullah48/tool.git
Author: asadullah48
License: MIT

Learning Source: Panaversity - Learn Agentic AI
Course: https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first
Website: https://panaversity.org/
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.getcwd())

def test_tools_import():
    """Test importing tools from tools_collection"""
    
    print("🔧 TESTING TOOLS COLLECTION IMPORT")
    print("=" * 50)
    
    try:
        # Try to import the tools collection
        print("📦 Importing tools_collection...")
        import tools_collection
        print("✅ tools_collection imported successfully!")
        
        # Check what's available
        print("\n📋 Available functions in tools_collection:")
        functions = [name for name in dir(tools_collection) if not name.startswith('_')]
        for i, func in enumerate(functions, 1):
            print(f"   {i}. {func}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 This is normal - tools_collection depends on config and other modules")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_config_loading():
    """Test loading configuration"""
    
    print("\n🔧 TESTING CONFIGURATION LOADING")
    print("=" * 50)
    
    try:
        print("📦 Importing config...")
        from config import config
        print("✅ Config imported successfully!")
        
        print(f"\n📋 Configuration details:")
        print(f"   Environment: {config.environment}")
        print(f"   Debug mode: {config.debug}")
        print(f"   Default model: {config.default_model}")
        
        # Check if weather API key is loaded
        if hasattr(config, 'weather_api_key') and config.weather_api_key:
            print(f"   Weather API: {config.weather_api_key[:10]}...")
        else:
            print("   Weather API: Not configured")
        
        return True
        
    except Exception as e:
        print(f"❌ Config loading error: {e}")
        return False

def test_manual_weather_tool():
    """Test weather functionality manually"""
    
    print("\n🌤️ TESTING WEATHER TOOL MANUALLY")
    print("=" * 50)
    
    try:
        import requests
        import os
        from dotenv import load_dotenv
        
        # Load environment
        load_dotenv()
        api_key = os.getenv('WEATHER_API_KEY')
        
        if not api_key:
            print("❌ Weather API key not found")
            return False
        
        print(f"✅ Weather API key loaded: {api_key[:10]}...")
        
        # Test weather for a city
        city = "London"
        print(f"\n🏙️ Getting weather for {city}...")
        
        url = "https://api.openweathermap.org/data/2.5/weather"
        params = {
            'q': city,
            'appid': api_key,
            'units': 'metric'
        }
        
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            temp = data['main']['temp']
            description = data['weather'][0]['description']
            
            print(f"   🌡️ Temperature: {temp}°C")
            print(f"   ☁️ Conditions: {description.title()}")
            print("   ✅ Weather tool working!")
            return True
        else:
            print(f"   ❌ API Error: {response.status_code}")
            return False
            
    except ImportError:
        print("❌ requests library not available")
        print("💡 Install with: pip install requests")
        return False
    except Exception as e:
        print(f"❌ Weather test error: {e}")
        return False

def test_learning_features():
    """Test learning features module"""
    
    print("\n🎓 TESTING LEARNING FEATURES")
    print("=" * 50)
    
    try:
        print("📦 Importing learning_features...")
        import learning_features
        print("✅ learning_features imported successfully!")
        
        # Check available classes
        classes = [name for name in dir(learning_features) if name[0].isupper()]
        print(f"\n📋 Available classes: {', '.join(classes)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Learning features error: {e}")
        print("💡 This module depends on other components")
        return False

def run_all_tests():
    """Run all tool tests"""
    
    print("🚀 AI AGENTS TOOLS LEARNING JOURNEY")
    print("Built with concepts from Panaversity's Learn Agentic AI course")
    print("🌐 https://panaversity.org/")
    print("📚 https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first")
    
    results = []
    
    # Run tests
    results.append(("Tools Collection Import", test_tools_import()))
    results.append(("Configuration Loading", test_config_loading()))
    results.append(("Weather Tool Manual", test_manual_weather_tool()))
    results.append(("Learning Features", test_learning_features()))
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 Results: {passed}/{len(results)} tests passed")
    
    print("\n🎓 LEARNING INSIGHTS:")
    print("-" * 30)
    print("1. Tools are modular functions that can be imported and used")
    print("2. Configuration management is crucial for API keys and settings")
    print("3. Error handling helps identify and resolve issues")
    print("4. External APIs (like weather) require proper authentication")
    print("5. AI agents use these tools to provide intelligent assistance")
    
    print("\n🚀 NEXT STEPS:")
    print("-" * 30)
    print("1. Try running: python main.py")
    print("2. Explore: streamlit run practices.py")
    print("3. Check individual tool files")
    print("4. Experiment with different API keys and configurations")

if __name__ == "__main__":
    run_all_tests()
