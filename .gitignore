# Enhanced AI Agents System - .gitignore
# Repository: https://github.com/asadullah48/tool.git

# =============================================================================
# ENVIRONMENT AND SECRETS
# =============================================================================

# Environment variables (contains API keys)
.env
.env.local
.env.development
.env.test
.env.production

# API keys and secrets
*.key
secrets/
credentials/

# =============================================================================
# PYTHON
# =============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# =============================================================================
# UV PACKAGE MANAGER
# =============================================================================

# UV lock files and cache
uv.lock
.uv/

# =============================================================================
# STREAMLIT
# =============================================================================

# Streamlit cache
.streamlit/

# =============================================================================
# LOGS AND DATA
# =============================================================================

# Application logs
logs/
*.log
*.log.*

# Data files
data/
*.db
*.sqlite
*.sqlite3

# Analytics and session data
analytics.json
session_*.json
user_progress.json

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# =============================================================================
# EDITORS AND IDEs
# =============================================================================

# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# =============================================================================
# OPERATING SYSTEM
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.fuse_hidden*
.directory
.Trash-*

# =============================================================================
# TEMPORARY AND CACHE FILES
# =============================================================================

# Temporary files
tmp/
temp/
*.tmp
*.temp
*.bak
*.backup
*.old

# Cache directories
.cache/
cache/

# =============================================================================
# PROJECT SPECIFIC
# =============================================================================

# Practice session data
practice_sessions/
student_progress/

# Generated documentation
docs/_build/
docs/build/

# Test outputs
test_outputs/
test_results/

# Backup files
*.backup
backup_*/

# Local configuration overrides
local_config.py
local_settings.py
