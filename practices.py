"""
Interactive AI Agents Practice Interface
=======================================

A comprehensive Streamlit-based practice interface for learning and teaching
AI agent function tools from basic to advanced levels.

Repository: https://github.com/asadullah48/tool.git
Author: asadullah48
License: MIT

Learning Source: Panaversity - Learn Agentic AI
Course: https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first
Website: https://panaversity.org/

Features:
- Progressive learning levels (Basic → Intermediate → Advanced → Expert)
- Interactive code editor with real-time execution
- Visual feedback and explanations
- Progress tracking and achievements
- Teaching mode for classroom use
- Peer learning activities

Usage:
    streamlit run practices.py
"""

import streamlit as st
import asyncio
import sys
import time
import json
from datetime import datetime
from pathlib import Path
import traceback

# Import our AI agents system with graceful fallback
DEMO_MODE = False
try:
    from config import config, logger
    from main import agent_manager, run_single_query
    from agents.tool import function_tool
    from tools_collection import track_tool_usage, explain_tool
    from utils import Formatter, performance_monitor
except ImportError as e:
    DEMO_MODE = True
    st.warning(f"⚠️ Running in Demo Mode: {e}")
    st.info("💡 Some features may be limited. Add your API keys to .env file for full functionality.")

    # Create mock objects for demo mode
    class MockFormatter:
        @staticmethod
        def format_time(seconds):
            if seconds < 60:
                return f"{seconds:.1f}s"
            else:
                minutes = int(seconds // 60)
                secs = seconds % 60
                return f"{minutes}m {secs:.0f}s"

    Formatter = MockFormatter()

    # Mock function_tool decorator
    def function_tool(name):
        def decorator(func):
            return func
        return decorator

# Configure Streamlit page
st.set_page_config(
    page_title="AI Agents Practice Lab | Panaversity",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded",
    menu_items={
        'Get Help': 'https://github.com/asadullah48/tool',
        'Report a bug': 'https://github.com/asadullah48/tool/issues',
        'About': """
        # AI Agents Practice Lab

        **Professional AI Agent Development Training Platform**

        Built with concepts from Panaversity's Learn Agentic AI course.

        🌐 [Panaversity.org](https://panaversity.org/)
        📚 [Course Materials](https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first)
        """
    }
)

# Custom CSS for professional styling
st.markdown("""
<style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

    /* Global Styles */
    .main {
        font-family: 'Inter', sans-serif;
    }

    /* Header Styling */
    .main-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        color: white;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .main-title {
        font-size: 3rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .main-subtitle {
        font-size: 1.2rem;
        font-weight: 300;
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
    }

    /* Professional Cards */
    .pro-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid #e1e5e9;
        margin: 1rem 0;
        transition: all 0.3s ease;
    }

    .pro-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    }

    /* Level Badges */
    .level-badge {
        display: inline-block;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.9rem;
        margin: 0.5rem;
        text-decoration: none;
    }

    .level-basic { background: #e8f5e8; color: #2e7d32; }
    .level-intermediate { background: #fff3e0; color: #f57c00; }
    .level-advanced { background: #fce4ec; color: #c2185b; }
    .level-expert { background: #f3e5f5; color: #7b1fa2; }

    /* Progress Indicators */
    .progress-container {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        margin: 1rem 0;
    }

    .progress-bar {
        background: linear-gradient(90deg, #4caf50, #8bc34a);
        height: 8px;
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    /* Panaversity Branding */
    .panaversity-brand {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
        margin: 1rem 0;
        border-left: 4px solid #ffd700;
    }

    .panaversity-compact {
        background: rgba(30, 60, 114, 0.05);
        border: 1px solid rgba(30, 60, 114, 0.1);
        padding: 0.8rem;
        border-radius: 8px;
        text-align: center;
        margin: 1rem 0;
    }

    /* Mode Selection */
    .mode-selector {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 12px;
        margin: 1rem 0;
        border: 2px solid #e9ecef;
    }

    /* Sidebar Styling */
    .sidebar-section {
        background: white;
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    /* Footer Styling */
    .footer-credits {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 2rem;
        border-radius: 15px;
        margin-top: 3rem;
        text-align: center;
        border-top: 3px solid #667eea;
    }

    /* Button Styling */
    .stButton > button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.5rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .stButton > button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    /* Code Editor Styling */
    .stTextArea textarea {
        font-family: 'Fira Code', 'Consolas', monospace;
        border-radius: 8px;
        border: 2px solid #e1e5e9;
    }

    /* Metrics Styling */
    .metric-container {
        background: white;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        border-left: 4px solid #667eea;
    }

    /* Success/Error Messages */
    .success-message {
        background: linear-gradient(135deg, #4caf50, #8bc34a);
        color: white;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
    }

    .error-message {
        background: linear-gradient(135deg, #f44336, #e57373);
        color: white;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
    }

    /* Hide Streamlit Branding */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}

    /* Custom Scrollbar */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
        background: #667eea;
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #5a6fd8;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state with proper defaults
def initialize_session_state():
    """Initialize all session state variables with safe defaults"""
    if 'practice_level' not in st.session_state:
        st.session_state.practice_level = 'Basic'
    if 'completed_exercises' not in st.session_state:
        st.session_state.completed_exercises = set()
    if 'user_progress' not in st.session_state:
        st.session_state.user_progress = {
            'total_attempts': 0,
            'successful_attempts': 0,
            'tools_created': 0,
            'session_start': datetime.now()
        }
    if 'practice_history' not in st.session_state:
        st.session_state.practice_history = []
    if 'show_welcome' not in st.session_state:
        st.session_state.show_welcome = True
    if 'selected_mode' not in st.session_state:
        st.session_state.selected_mode = "learning"
    if 'selected_exercise_id' not in st.session_state:
        st.session_state.selected_exercise_id = None

# Initialize session state
initialize_session_state()

# Practice exercises database
PRACTICE_EXERCISES = {
    'Basic': {
        'title': '🟢 Basic Level - Function Tool Fundamentals',
        'description': 'Learn the basics of creating function tools for AI agents',
        'exercises': [
            {
                'id': 'basic_1',
                'title': 'Simple Greeting Tool',
                'description': 'Create a function tool that greets users by name',
                'instruction': '''Create a function tool called "greet_user" that:
1. Takes a name parameter (string)
2. Returns a greeting message
3. Uses the @function_tool decorator''',
                'template': '''@function_tool("greet_user")
def greet_user(name: str) -> str:
    """
    Greet a user by name.
    
    Args:
        name: The user's name
    
    Returns:
        A greeting message
    """
    # Your code here
    pass''',
                'solution': '''@function_tool("greet_user")
def greet_user(name: str) -> str:
    """
    Greet a user by name.
    
    Args:
        name: The user's name
    
    Returns:
        A greeting message
    """
    return f"Hello, {name}! Welcome to AI Agents Practice Lab! 👋"''',
                'test_cases': [
                    {'input': 'Alice', 'expected_contains': ['Hello', 'Alice']},
                    {'input': 'Bob', 'expected_contains': ['Hello', 'Bob']}
                ]
            },
            {
                'id': 'basic_2',
                'title': 'Simple Calculator Tool',
                'description': 'Create a basic arithmetic calculator tool',
                'instruction': '''Create a function tool called "basic_calculator" that:
1. Takes two numbers and an operation (+, -, *, /)
2. Performs the calculation
3. Returns the result with proper formatting''',
                'template': '''@function_tool("basic_calculator")
def basic_calculator(num1: float, num2: float, operation: str) -> str:
    """
    Perform basic arithmetic operations.
    
    Args:
        num1: First number
        num2: Second number
        operation: Operation (+, -, *, /)
    
    Returns:
        Calculation result
    """
    # Your code here
    pass''',
                'solution': '''@function_tool("basic_calculator")
def basic_calculator(num1: float, num2: float, operation: str) -> str:
    """
    Perform basic arithmetic operations.
    
    Args:
        num1: First number
        num2: Second number
        operation: Operation (+, -, *, /)
    
    Returns:
        Calculation result
    """
    try:
        if operation == '+':
            result = num1 + num2
        elif operation == '-':
            result = num1 - num2
        elif operation == '*':
            result = num1 * num2
        elif operation == '/':
            if num2 == 0:
                return "❌ Error: Division by zero is not allowed"
            result = num1 / num2
        else:
            return f"❌ Error: Unknown operation '{operation}'"
        
        return f"🧮 {num1} {operation} {num2} = {result}"
    except Exception as e:
        return f"❌ Error: {str(e)}"''',
                'test_cases': [
                    {'input': (10, 5, '+'), 'expected_contains': ['15']},
                    {'input': (10, 2, '*'), 'expected_contains': ['20']},
                    {'input': (10, 0, '/'), 'expected_contains': ['Error', 'zero']}
                ]
            },
            {
                'id': 'basic_3',
                'title': 'Text Length Counter',
                'description': 'Create a tool that counts characters and words in text',
                'instruction': '''Create a function tool called "text_counter" that:
1. Takes a text string as input
2. Counts characters, words, and sentences
3. Returns a formatted summary''',
                'template': '''@function_tool("text_counter")
def text_counter(text: str) -> str:
    """
    Count characters, words, and sentences in text.
    
    Args:
        text: Input text to analyze
    
    Returns:
        Text analysis summary
    """
    # Your code here
    pass''',
                'solution': '''@function_tool("text_counter")
def text_counter(text: str) -> str:
    """
    Count characters, words, and sentences in text.
    
    Args:
        text: Input text to analyze
    
    Returns:
        Text analysis summary
    """
    char_count = len(text)
    word_count = len(text.split())
    sentence_count = text.count('.') + text.count('!') + text.count('?')
    
    return (
        f"📝 Text Analysis:\\n"
        f"🔤 Characters: {char_count}\\n"
        f"📝 Words: {word_count}\\n"
        f"📄 Sentences: {sentence_count}"
    )''',
                'test_cases': [
                    {'input': 'Hello world!', 'expected_contains': ['Characters: 12', 'Words: 2']},
                    {'input': 'AI is amazing. It helps us learn.', 'expected_contains': ['Sentences: 2']}
                ]
            }
        ]
    },
    'Intermediate': {
        'title': '🟡 Intermediate Level - Enhanced Function Tools',
        'description': 'Build more sophisticated tools with error handling and validation',
        'exercises': [
            {
                'id': 'inter_1',
                'title': 'Data Validator Tool',
                'description': 'Create a tool that validates different types of data',
                'instruction': '''Create a function tool called "data_validator" that:
1. Takes data and data_type parameters
2. Validates email, phone, or URL formats
3. Returns validation results with helpful messages''',
                'template': '''@function_tool("data_validator")
def data_validator(data: str, data_type: str) -> str:
    """
    Validate different types of data.
    
    Args:
        data: Data to validate
        data_type: Type of validation (email, phone, url)
    
    Returns:
        Validation result
    """
    import re
    # Your code here
    pass''',
                'solution': '''@function_tool("data_validator")
def data_validator(data: str, data_type: str) -> str:
    """
    Validate different types of data.
    
    Args:
        data: Data to validate
        data_type: Type of validation (email, phone, url)
    
    Returns:
        Validation result
    """
    import re
    
    if data_type.lower() == 'email':
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$'
        is_valid = re.match(pattern, data) is not None
        return f"📧 Email '{data}' is {'✅ valid' if is_valid else '❌ invalid'}"
    
    elif data_type.lower() == 'phone':
        pattern = r'^\\+?[1-9]\\d{1,14}$'
        clean_phone = re.sub(r'[\\s\\-\\(\\)]', '', data)
        is_valid = re.match(pattern, clean_phone) is not None
        return f"📱 Phone '{data}' is {'✅ valid' if is_valid else '❌ invalid'}"
    
    elif data_type.lower() == 'url':
        pattern = r'^https?://(?:[-\\w.])+(?:\\:[0-9]+)?(?:/(?:[\\w/_.])*(?:\\?(?:[\\w&=%.])*)?(?:\\#(?:[\\w.])*)?)?$'
        is_valid = re.match(pattern, data) is not None
        return f"🌐 URL '{data}' is {'✅ valid' if is_valid else '❌ invalid'}"
    
    else:
        return f"❌ Unknown data type: {data_type}. Use 'email', 'phone', or 'url'"''',
                'test_cases': [
                    {'input': ('<EMAIL>', 'email'), 'expected_contains': ['valid']},
                    {'input': ('invalid-email', 'email'), 'expected_contains': ['invalid']},
                    {'input': ('https://example.com', 'url'), 'expected_contains': ['valid']}
                ]
            },
            {
                'id': 'inter_2',
                'title': 'Smart List Processor',
                'description': 'Create a tool that processes lists with multiple operations',
                'instruction': '''Create a function tool called "list_processor" that:
1. Takes a list of numbers and an operation type
2. Supports operations: sum, average, max, min, sort
3. Returns formatted results with statistics''',
                'template': '''@function_tool("list_processor")
def list_processor(numbers: list, operation: str) -> str:
    """
    Process a list of numbers with various operations.

    Args:
        numbers: List of numbers to process
        operation: Operation type (sum, average, max, min, sort)

    Returns:
        Processed result with formatting
    """
    # Your code here
    pass''',
                'solution': '''@function_tool("list_processor")
def list_processor(numbers: list, operation: str) -> str:
    """
    Process a list of numbers with various operations.

    Args:
        numbers: List of numbers to process
        operation: Operation type (sum, average, max, min, sort)

    Returns:
        Processed result with formatting
    """
    if not numbers:
        return "❌ Error: Empty list provided"

    try:
        # Convert to numbers if needed
        nums = [float(x) for x in numbers]

        if operation.lower() == 'sum':
            result = sum(nums)
            return f"➕ Sum of {nums}: {result}"
        elif operation.lower() == 'average':
            result = sum(nums) / len(nums)
            return f"📊 Average of {nums}: {result:.2f}"
        elif operation.lower() == 'max':
            result = max(nums)
            return f"⬆️ Maximum of {nums}: {result}"
        elif operation.lower() == 'min':
            result = min(nums)
            return f"⬇️ Minimum of {nums}: {result}"
        elif operation.lower() == 'sort':
            result = sorted(nums)
            return f"🔢 Sorted {nums}: {result}"
        else:
            return f"❌ Unknown operation: {operation}. Use: sum, average, max, min, sort"
    except ValueError:
        return "❌ Error: All items must be numbers"
    except Exception as e:
        return f"❌ Error: {str(e)}"''',
                'test_cases': [
                    {'input': ([1, 2, 3, 4, 5], 'sum'), 'expected_contains': ['15']},
                    {'input': ([10, 20, 30], 'average'), 'expected_contains': ['20']},
                    {'input': ([3, 1, 4, 1, 5], 'sort'), 'expected_contains': ['[1, 1, 3, 4, 5]']}
                ]
            }
        ]
    },
    'Advanced': {
        'title': '🟠 Advanced Level - Complex Function Tools',
        'description': 'Build sophisticated tools with advanced features and integrations',
        'exercises': [
            {
                'id': 'adv_1',
                'title': 'JSON Data Processor',
                'description': 'Create a tool that processes and analyzes JSON data',
                'instruction': '''Create a function tool called "json_processor" that:
1. Takes JSON string and operation type
2. Supports: validate, extract_keys, count_items, find_value
3. Handles errors gracefully and provides detailed feedback''',
                'template': '''@function_tool("json_processor")
def json_processor(json_data: str, operation: str, key: str = "") -> str:
    """
    Process and analyze JSON data.

    Args:
        json_data: JSON string to process
        operation: Operation type (validate, extract_keys, count_items, find_value)
        key: Key to search for (used with find_value operation)

    Returns:
        Processing result
    """
    import json
    # Your code here
    pass''',
                'solution': '''@function_tool("json_processor")
def json_processor(json_data: str, operation: str, key: str = "") -> str:
    """
    Process and analyze JSON data.

    Args:
        json_data: JSON string to process
        operation: Operation type (validate, extract_keys, count_items, find_value)
        key: Key to search for (used with find_value operation)

    Returns:
        Processing result
    """
    import json

    try:
        # Parse JSON
        data = json.loads(json_data)

        if operation == 'validate':
            return "✅ JSON is valid and properly formatted"

        elif operation == 'extract_keys':
            if isinstance(data, dict):
                keys = list(data.keys())
                return f"🔑 Keys found: {keys}"
            else:
                return "❌ JSON is not an object (no keys to extract)"

        elif operation == 'count_items':
            if isinstance(data, dict):
                count = len(data)
                return f"📊 Object has {count} key-value pairs"
            elif isinstance(data, list):
                count = len(data)
                return f"📊 Array has {count} items"
            else:
                return "📊 Single value (not countable)"

        elif operation == 'find_value':
            if not key:
                return "❌ Key parameter required for find_value operation"
            if isinstance(data, dict) and key in data:
                return f"🎯 Found '{key}': {data[key]}"
            else:
                return f"❌ Key '{key}' not found in JSON"

        else:
            return f"❌ Unknown operation: {operation}"

    except json.JSONDecodeError as e:
        return f"❌ Invalid JSON: {str(e)}"
    except Exception as e:
        return f"❌ Error: {str(e)}"''',
                'test_cases': [
                    {'input': ('{"name": "Alice", "age": 30}', 'validate'), 'expected_contains': ['valid']},
                    {'input': ('{"name": "Alice", "age": 30}', 'extract_keys'), 'expected_contains': ['name', 'age']},
                    {'input': ('{"name": "Alice", "age": 30}', 'find_value', 'name'), 'expected_contains': ['Alice']}
                ]
            },
            {
                'id': 'adv_2',
                'title': 'Performance Monitor Tool',
                'description': 'Create a tool that monitors and reports function performance',
                'instruction': '''Create a function tool called "performance_monitor" that:
1. Takes a function name and execution time
2. Tracks performance metrics over multiple calls
3. Provides performance analysis and recommendations''',
                'template': '''@function_tool("performance_monitor")
def performance_monitor(func_name: str, execution_time: float, action: str = "record") -> str:
    """
    Monitor and analyze function performance.

    Args:
        func_name: Name of the function being monitored
        execution_time: Execution time in seconds
        action: Action to perform (record, report, reset)

    Returns:
        Performance analysis result
    """
    # Use a global dictionary to store performance data
    if not hasattr(performance_monitor, 'metrics'):
        performance_monitor.metrics = {}

    # Your code here
    pass''',
                'solution': '''@function_tool("performance_monitor")
def performance_monitor(func_name: str, execution_time: float, action: str = "record") -> str:
    """
    Monitor and analyze function performance.

    Args:
        func_name: Name of the function being monitored
        execution_time: Execution time in seconds
        action: Action to perform (record, report, reset)

    Returns:
        Performance analysis result
    """
    # Use a global dictionary to store performance data
    if not hasattr(performance_monitor, 'metrics'):
        performance_monitor.metrics = {}

    if action == "record":
        if func_name not in performance_monitor.metrics:
            performance_monitor.metrics[func_name] = {
                'calls': 0,
                'total_time': 0,
                'min_time': float('inf'),
                'max_time': 0,
                'times': []
            }

        metric = performance_monitor.metrics[func_name]
        metric['calls'] += 1
        metric['total_time'] += execution_time
        metric['min_time'] = min(metric['min_time'], execution_time)
        metric['max_time'] = max(metric['max_time'], execution_time)
        metric['times'].append(execution_time)

        avg_time = metric['total_time'] / metric['calls']

        return f"📊 Recorded {func_name}: {execution_time:.3f}s (avg: {avg_time:.3f}s, calls: {metric['calls']})"

    elif action == "report":
        if func_name not in performance_monitor.metrics:
            return f"❌ No performance data for '{func_name}'"

        metric = performance_monitor.metrics[func_name]
        avg_time = metric['total_time'] / metric['calls']

        # Performance assessment
        if avg_time < 0.1:
            assessment = "🟢 Excellent performance"
        elif avg_time < 0.5:
            assessment = "🟡 Good performance"
        elif avg_time < 1.0:
            assessment = "🟠 Moderate performance"
        else:
            assessment = "🔴 Needs optimization"

        return (
            f"📈 Performance Report for '{func_name}':\\n"
            f"🔢 Total Calls: {metric['calls']}\\n"
            f"⏱️ Average Time: {avg_time:.3f}s\\n"
            f"⚡ Fastest: {metric['min_time']:.3f}s\\n"
            f"🐌 Slowest: {metric['max_time']:.3f}s\\n"
            f"📊 Assessment: {assessment}"
        )

    elif action == "reset":
        if func_name in performance_monitor.metrics:
            del performance_monitor.metrics[func_name]
            return f"🔄 Reset performance data for '{func_name}'"
        else:
            return f"❌ No performance data to reset for '{func_name}'"

    else:
        return f"❌ Unknown action: {action}. Use: record, report, reset"''',
                'test_cases': [
                    {'input': ('test_func', 0.5, 'record'), 'expected_contains': ['Recorded', 'test_func', '0.500s']},
                    {'input': ('test_func', 0.3, 'record'), 'expected_contains': ['calls: 2']},
                    {'input': ('test_func', 0, 'report'), 'expected_contains': ['Performance Report', 'Average Time']}
                ]
            }
        ]
    },
    'Expert': {
        'title': '🔴 Expert Level - Advanced AI Integration',
        'description': 'Master-level tools with AI agent integration and complex workflows',
        'exercises': [
            {
                'id': 'expert_1',
                'title': 'Multi-Agent Coordinator',
                'description': 'Create a tool that coordinates multiple AI agents for complex tasks',
                'instruction': '''Create a function tool called "agent_coordinator" that:
1. Takes a task description and agent types
2. Breaks down the task for different agents
3. Coordinates the workflow and combines results''',
                'template': '''@function_tool("agent_coordinator")
def agent_coordinator(task: str, agent_types: list, coordination_mode: str = "sequential") -> str:
    """
    Coordinate multiple AI agents for complex tasks.

    Args:
        task: Description of the task to accomplish
        agent_types: List of agent types to coordinate
        coordination_mode: How to coordinate (sequential, parallel, hierarchical)

    Returns:
        Coordination plan and workflow
    """
    # Your code here
    pass''',
                'solution': '''@function_tool("agent_coordinator")
def agent_coordinator(task: str, agent_types: list, coordination_mode: str = "sequential") -> str:
    """
    Coordinate multiple AI agents for complex tasks.

    Args:
        task: Description of the task to accomplish
        agent_types: List of agent types to coordinate
        coordination_mode: How to coordinate (sequential, parallel, hierarchical)

    Returns:
        Coordination plan and workflow
    """
    if not agent_types:
        return "❌ Error: No agent types provided"

    # Define agent capabilities
    agent_capabilities = {
        'researcher': 'Information gathering and analysis',
        'technical': 'Technical implementation and code',
        'teacher': 'Educational content and explanations',
        'analyst': 'Data analysis and insights',
        'creative': 'Creative solutions and brainstorming',
        'tutor': 'Personalized guidance and feedback'
    }

    # Create coordination plan
    plan = f"🤝 Multi-Agent Coordination Plan\\n"
    plan += f"📋 Task: {task}\\n"
    plan += f"🔄 Mode: {coordination_mode}\\n"
    plan += f"👥 Agents: {', '.join(agent_types)}\\n\\n"

    if coordination_mode == "sequential":
        plan += "📊 Sequential Workflow:\\n"
        for i, agent in enumerate(agent_types, 1):
            capability = agent_capabilities.get(agent, 'General assistance')
            plan += f"  {i}. {agent.title()} Agent: {capability}\\n"

    elif coordination_mode == "parallel":
        plan += "⚡ Parallel Workflow:\\n"
        for agent in agent_types:
            capability = agent_capabilities.get(agent, 'General assistance')
            plan += f"  • {agent.title()} Agent: {capability}\\n"

    elif coordination_mode == "hierarchical":
        plan += "🏗️ Hierarchical Workflow:\\n"
        if 'researcher' in agent_types:
            plan += "  1. Researcher Agent: Initial analysis\\n"
        if 'technical' in agent_types:
            plan += "  2. Technical Agent: Implementation\\n"
        if 'teacher' in agent_types:
            plan += "  3. Teacher Agent: Documentation\\n"

    plan += f"\\n✅ Coordination plan ready for execution!"
    return plan''',
                'test_cases': [
                    {'input': ('Analyze data', ['researcher', 'analyst'], 'sequential'), 'expected_contains': ['Sequential Workflow', 'researcher', 'analyst']},
                    {'input': ('Create tutorial', ['teacher', 'creative'], 'parallel'), 'expected_contains': ['Parallel Workflow']},
                    {'input': ('Complex project', ['researcher', 'technical', 'teacher'], 'hierarchical'), 'expected_contains': ['Hierarchical Workflow']}
                ]
            }
        ]
    }
}

def main():
    """Main Streamlit application"""

    # Professional Header
    if st.session_state.show_welcome:
        st.markdown("""
        <div class="main-header">
            <h1 class="main-title">🚀 AI Agents Practice Lab</h1>
            <p class="main-subtitle">Professional AI Agent Development Training Platform</p>
            <div style="margin-top: 1.5rem;">
                <span style="background: rgba(255,255,255,0.2); padding: 0.5rem 1rem; border-radius: 20px; margin: 0 0.5rem; font-size: 0.9rem;">
                    🎓 Powered by Panaversity
                </span>
                <span style="background: rgba(255,255,255,0.2); padding: 0.5rem 1rem; border-radius: 20px; margin: 0 0.5rem; font-size: 0.9rem;">
                    🌟 Learn Agentic AI
                </span>
            </div>
        </div>
        """, unsafe_allow_html=True)

        # Welcome Features Section
        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("""
            <div class="pro-card">
                <h3 style="color: #667eea; margin-top: 0;">🎯 Progressive Learning</h3>
                <p>Master AI agent development through 4 skill levels:</p>
                <div class="level-badge level-basic">🟢 Basic</div>
                <div class="level-badge level-intermediate">🟡 Intermediate</div>
                <div class="level-badge level-advanced">🟠 Advanced</div>
                <div class="level-badge level-expert">🔴 Expert</div>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            st.markdown("""
            <div class="pro-card">
                <h3 style="color: #667eea; margin-top: 0;">👩‍🏫 Teaching Tools</h3>
                <p>Perfect for educators and peer learning:</p>
                <ul style="margin: 0; padding-left: 1.2rem;">
                    <li>Interactive demonstrations</li>
                    <li>Lesson plan generator</li>
                    <li>Live coding sessions</li>
                    <li>Progress tracking</li>
                </ul>
            </div>
            """, unsafe_allow_html=True)

        with col3:
            st.markdown("""
            <div class="pro-card">
                <h3 style="color: #667eea; margin-top: 0;">📊 Analytics</h3>
                <p>Track your learning journey:</p>
                <ul style="margin: 0; padding-left: 1.2rem;">
                    <li>Real-time progress</li>
                    <li>Performance metrics</li>
                    <li>Achievement system</li>
                    <li>Learning insights</li>
                </ul>
            </div>
            """, unsafe_allow_html=True)

        # Panaversity Credits in Welcome
        st.markdown("""
        <div class="panaversity-brand">
            <h4 style="margin: 0 0 0.5rem 0;">🎓 Educational Foundation</h4>
            <p style="margin: 0 0 1rem 0;">This platform is built upon concepts learned from Panaversity's innovative Learn Agentic AI course</p>
            <div>
                <a href="https://panaversity.org/" target="_blank" style="color: #ffd700; text-decoration: none; margin: 0 1rem;">
                    🌐 Panaversity.org
                </a>
                <a href="https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first" target="_blank" style="color: #ffd700; text-decoration: none; margin: 0 1rem;">
                    📚 Course Materials
                </a>
            </div>
        </div>
        """, unsafe_allow_html=True)

        # Start Learning Button
        col1, col2, col3 = st.columns([1, 1, 1])
        with col2:
            if st.button("🚀 Start Your Learning Journey", key="start_learning", help="Begin practicing AI agent development"):
                st.session_state.show_welcome = False
                st.rerun()
    else:
        # Compact Professional Header
        st.markdown("""
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 1rem; border-radius: 10px; margin-bottom: 1rem; color: white; text-align: center;">
            <h2 style="margin: 0; font-weight: 600;">🚀 AI Agents Practice Lab</h2>
        </div>
        """, unsafe_allow_html=True)

        # Compact Panaversity Credits
        st.markdown("""
        <div class="panaversity-compact">
            <p style="margin: 0; font-size: 0.9rem; color: #1e3c72;">
                🎓 Built with concepts from <a href="https://panaversity.org/" target="_blank" style="color: #1e3c72; font-weight: 600;">Panaversity's Learn Agentic AI</a> course
            </p>
        </div>
        """, unsafe_allow_html=True)

    # Professional Mode Selection
    st.markdown("""
    <div class="mode-selector">
        <h3 style="margin: 0 0 1rem 0; color: #333; text-align: center;">Choose Your Learning Path</h3>
    </div>
    """, unsafe_allow_html=True)

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("🎓 Learning Mode", key="mode_learning", help="Individual practice and skill development"):
            st.session_state.selected_mode = "learning"

    with col2:
        if st.button("👩‍🏫 Teaching Mode", key="mode_teaching", help="Classroom demonstrations and lesson planning"):
            st.session_state.selected_mode = "teaching"

    with col3:
        if st.button("📊 Analytics Dashboard", key="mode_analytics", help="Progress tracking and performance insights"):
            st.session_state.selected_mode = "analytics"

    # Initialize mode if not set
    if 'selected_mode' not in st.session_state:
        st.session_state.selected_mode = "learning"

    mode = st.session_state.selected_mode

    # Ensure selected_exercise_id is initialized
    if 'selected_exercise_id' not in st.session_state:
        st.session_state.selected_exercise_id = None

    if mode == "learning":
        learning_mode()
    elif mode == "teaching":
        teaching_mode()
    elif mode == "analytics":
        analytics_dashboard()

def learning_mode():
    """Individual learning mode"""

    # Professional Sidebar
    with st.sidebar:
        # Panaversity Branding in Sidebar
        st.markdown("""
        <div class="sidebar-section" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;">
            <h4 style="color: white; margin: 0 0 0.5rem 0; font-size: 1.1rem;">🎓 Powered by Panaversity</h4>
            <p style="margin: 0 0 0.8rem 0; font-size: 0.85rem; line-height: 1.4; opacity: 0.9;">
                Professional AI education from <strong>Panaversity's Learn Agentic AI</strong> course
            </p>
            <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                <a href="https://panaversity.org/" target="_blank" style="color: #ffd700; text-decoration: none; font-size: 0.8rem;">
                    🌐 Website
                </a>
                <a href="https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first" target="_blank" style="color: #ffd700; text-decoration: none; font-size: 0.8rem;">
                    📚 Course
                </a>
            </div>
        </div>
        """, unsafe_allow_html=True)

        # Practice Controls Section
        st.markdown("""
        <div class="sidebar-section">
            <h3 style="color: #333; margin: 0 0 1rem 0; font-size: 1.2rem;">🎯 Practice Controls</h3>
        </div>
        """, unsafe_allow_html=True)
        
        # Level selection with safety check
        available_levels = ['Basic', 'Intermediate', 'Advanced', 'Expert']
        try:
            current_index = available_levels.index(st.session_state.practice_level)
        except (ValueError, AttributeError):
            current_index = 0
            st.session_state.practice_level = 'Basic'

        level = st.selectbox(
            "Choose Practice Level:",
            options=available_levels,
            index=current_index
        )
        st.session_state.practice_level = level
        
        # Professional Progress Display
        st.markdown("""
        <div class="sidebar-section">
            <h3 style="color: #333; margin: 0 0 1rem 0; font-size: 1.2rem;">📊 Your Progress</h3>
        </div>
        """, unsafe_allow_html=True)

        progress = st.session_state.user_progress
        success_rate = (progress['successful_attempts'] / max(progress['total_attempts'], 1)) * 100
        session_time = datetime.now() - progress['session_start']

        # Progress Metrics with Professional Styling
        st.markdown(f"""
        <div class="metric-container" style="margin-bottom: 0.5rem;">
            <div style="font-size: 1.5rem; font-weight: 600; color: #667eea;">{progress['total_attempts']}</div>
            <div style="font-size: 0.8rem; color: #666;">Total Attempts</div>
        </div>
        """, unsafe_allow_html=True)

        st.markdown(f"""
        <div class="metric-container" style="margin-bottom: 0.5rem;">
            <div style="font-size: 1.5rem; font-weight: 600; color: #4caf50;">{progress['tools_created']}</div>
            <div style="font-size: 0.8rem; color: #666;">Tools Created</div>
        </div>
        """, unsafe_allow_html=True)

        st.markdown(f"""
        <div class="metric-container" style="margin-bottom: 0.5rem;">
            <div style="font-size: 1.5rem; font-weight: 600; color: #ff9800;">{success_rate:.1f}%</div>
            <div style="font-size: 0.8rem; color: #666;">Success Rate</div>
        </div>
        """, unsafe_allow_html=True)

        st.markdown(f"""
        <div class="metric-container" style="margin-bottom: 1rem;">
            <div style="font-size: 1.2rem; font-weight: 600; color: #9c27b0;">{Formatter.format_time(session_time.total_seconds())}</div>
            <div style="font-size: 0.8rem; color: #666;">Session Time</div>
        </div>
        """, unsafe_allow_html=True)
        
        # Completed Exercises with Professional Styling
        if st.session_state.completed_exercises:
            st.markdown("""
            <div class="sidebar-section">
                <h3 style="color: #333; margin: 0 0 1rem 0; font-size: 1.2rem;">🏆 Achievements</h3>
            </div>
            """, unsafe_allow_html=True)

            for exercise_id in st.session_state.completed_exercises:
                st.markdown(f"""
                <div style="background: linear-gradient(135deg, #4caf50, #8bc34a); color: white; padding: 0.5rem; border-radius: 6px; margin: 0.3rem 0; font-size: 0.85rem;">
                    ✅ {exercise_id.replace('_', ' ').title()}
                </div>
                """, unsafe_allow_html=True)

        # Professional Reset Button
        st.markdown("<br>", unsafe_allow_html=True)
        if st.button("🔄 Reset Progress", help="Clear all progress and start fresh"):
            st.session_state.completed_exercises = set()
            st.session_state.user_progress = {
                'total_attempts': 0,
                'successful_attempts': 0,
                'tools_created': 0,
                'session_start': datetime.now()
            }
            st.session_state.practice_history = []
            st.rerun()
    
    # Professional Main Content Area
    if level in PRACTICE_EXERCISES:
        level_data = PRACTICE_EXERCISES[level]

        # Level Header with Professional Styling
        level_colors = {
            'Basic': '#4caf50',
            'Intermediate': '#ff9800',
            'Advanced': '#e91e63',
            'Expert': '#9c27b0'
        }

        level_name = level_data['title'].split(' - ')[0].replace('🟢 ', '').replace('🟡 ', '').replace('🟠 ', '').replace('🔴 ', '')
        level_color = level_colors.get(level_name, '#667eea')

        st.markdown(f"""
        <div class="pro-card" style="border-left: 4px solid {level_color};">
            <h2 style="color: {level_color}; margin: 0 0 0.5rem 0;">{level_data['title']}</h2>
            <p style="margin: 0; color: #666; font-size: 1.1rem;">{level_data['description']}</p>
        </div>
        """, unsafe_allow_html=True)

        # Professional Exercise Selection
        st.markdown("""
        <div class="pro-card">
            <h3 style="color: #333; margin: 0 0 1rem 0;">📝 Choose Your Exercise</h3>
        </div>
        """, unsafe_allow_html=True)

        # Exercise Cards Layout
        cols = st.columns(len(level_data['exercises']))
        selected_exercise_id = None

        for i, exercise in enumerate(level_data['exercises']):
            with cols[i]:
                is_completed = exercise['id'] in st.session_state.completed_exercises
                card_style = "border: 2px solid #4caf50;" if is_completed else "border: 2px solid #e1e5e9;"

                if st.button(
                    f"{'✅' if is_completed else '📝'} {exercise['title']}",
                    key=f"exercise_{exercise['id']}",
                    help=exercise['description']
                ):
                    selected_exercise_id = exercise['id']

        # Display selected exercise or default to first
        if 'selected_exercise_id' not in st.session_state or st.session_state.selected_exercise_id is None:
            st.session_state.selected_exercise_id = level_data['exercises'][0]['id']

        if selected_exercise_id:
            st.session_state.selected_exercise_id = selected_exercise_id

        # Find and display the selected exercise with error handling
        try:
            exercise = next(ex for ex in level_data['exercises'] if ex['id'] == st.session_state.selected_exercise_id)
        except StopIteration:
            # If selected exercise not found, default to first exercise
            st.session_state.selected_exercise_id = level_data['exercises'][0]['id']
            exercise = level_data['exercises'][0]

        display_exercise(exercise)

    else:
        st.markdown("""
        <div class="pro-card" style="text-align: center; padding: 3rem;">
            <h2 style="color: #ff9800; margin: 0 0 1rem 0;">🚧 Coming Soon!</h2>
            <p style="font-size: 1.2rem; color: #666; margin: 0 0 1rem 0;">Level '{level}' is under development</p>
            <p style="color: #888;">Currently available: <strong>Basic</strong> and <strong>Intermediate</strong> levels</p>
        </div>
        """, unsafe_allow_html=True)

    # Professional Footer with Enhanced Credits
    st.markdown("""
    <div class="footer-credits">
        <h3 style="color: #333; margin: 0 0 1.5rem 0; font-weight: 600;">🙏 Educational Foundation & Acknowledgments</h3>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-bottom: 2rem;">
            <div style="text-align: left;">
                <h4 style="color: #1e3c72; margin: 0 0 0.5rem 0;">🎓 Primary Learning Source</h4>
                <p style="margin: 0 0 0.5rem 0; color: #333;">
                    This platform is built upon concepts learned from <strong>Panaversity's Learn Agentic AI</strong> course
                </p>
                <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                    <a href="https://panaversity.org/" target="_blank" style="color: #1e3c72; text-decoration: none; font-weight: 600;">
                        🌐 Panaversity.org
                    </a>
                    <a href="https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first" target="_blank" style="color: #1e3c72; text-decoration: none; font-weight: 600;">
                        📚 Course Materials
                    </a>
                </div>
            </div>

            <div style="text-align: left;">
                <h4 style="color: #667eea; margin: 0 0 0.5rem 0;">🚀 Project Information</h4>
                <p style="margin: 0 0 0.5rem 0; color: #333;">
                    Open-source educational platform for AI agent development
                </p>
                <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                    <a href="https://github.com/asadullah48/tool.git" target="_blank" style="color: #667eea; text-decoration: none; font-weight: 600;">
                        📂 Repository
                    </a>
                    <a href="https://github.com/asadullah48" target="_blank" style="color: #667eea; text-decoration: none; font-weight: 600;">
                        👨‍💻 Author
                    </a>
                </div>
            </div>
        </div>

        <div style="border-top: 1px solid #dee2e6; padding-top: 1rem; color: #666; font-size: 0.9rem;">
            <p style="margin: 0;">
                Built with ❤️ for education | Empowering the next generation of AI developers |
                <strong>MIT License</strong> | © 2024 asadullah48
            </p>
        </div>
    </div>
    """, unsafe_allow_html=True)

def display_exercise(exercise):
    """Display and handle a practice exercise with professional styling"""

    # Exercise Header
    st.markdown(f"""
    <div class="pro-card" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
        <h2 style="color: #333; margin: 0 0 0.5rem 0;">📝 {exercise['title']}</h2>
        <p style="margin: 0; color: #666; font-size: 1.1rem;">{exercise['description']}</p>
    </div>
    """, unsafe_allow_html=True)

    # Professional Instructions Section
    with st.expander("📋 Exercise Instructions", expanded=True):
        st.markdown(f"""
        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; border-left: 4px solid #667eea;">
            {exercise['instruction'].replace(chr(10), '<br>')}
        </div>
        """, unsafe_allow_html=True)

    # Professional Code Editor Section
    st.markdown("""
    <div class="pro-card">
        <h3 style="color: #333; margin: 0 0 1rem 0;">💻 Interactive Code Editor</h3>
        <p style="margin: 0 0 1rem 0; color: #666; font-size: 0.9rem;">
            Write your function tool below. Use the template as a starting point and implement the required functionality.
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # Show template or user's previous attempt
    default_code = exercise['template']
    if f"code_{exercise['id']}" in st.session_state:
        default_code = st.session_state[f"code_{exercise['id']}"]
    
    user_code = st.text_area(
        "Write your function tool:",
        value=default_code,
        height=300,
        key=f"editor_{exercise['id']}"
    )
    
    # Save code to session state
    st.session_state[f"code_{exercise['id']}"] = user_code
    
    # Professional Action Buttons
    st.markdown("""
    <div class="pro-card">
        <h4 style="color: #333; margin: 0 0 1rem 0;">🎯 Actions</h4>
    </div>
    """, unsafe_allow_html=True)

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if st.button("🧪 Test Code", key=f"test_{exercise['id']}", help="Run your code against test cases"):
            test_user_code(exercise, user_code)

    with col2:
        if st.button("💡 Get Hint", key=f"hint_{exercise['id']}", help="Get a helpful hint for this exercise"):
            show_hint(exercise)

    with col3:
        if st.button("✅ View Solution", key=f"solution_{exercise['id']}", help="See the complete solution"):
            show_solution(exercise)

    with col4:
        if st.button("🔄 Reset Code", key=f"reset_{exercise['id']}", help="Reset to the original template"):
            st.session_state[f"code_{exercise['id']}"] = exercise['template']
            st.rerun()

def test_user_code(exercise, user_code):
    """Test user's code against test cases"""
    
    st.session_state.user_progress['total_attempts'] += 1
    
    try:
        # Create a safe execution environment
        exec_globals = {
            '__builtins__': __builtins__,
            'function_tool': function_tool,
            're': __import__('re'),
            'math': __import__('math'),
            'datetime': __import__('datetime'),
            'json': __import__('json')
        }
        
        # Execute user code
        exec(user_code, exec_globals)
        
        # Extract the function name from the decorator
        import re
        func_name_match = re.search(r'@function_tool\("([^"]+)"\)', user_code)
        if not func_name_match:
            st.error("❌ No @function_tool decorator found!")
            return
        
        func_name = func_name_match.group(1)
        
        # Get the actual function
        actual_func_name = user_code.split('def ')[1].split('(')[0].strip()
        if actual_func_name not in exec_globals:
            st.error(f"❌ Function '{actual_func_name}' not found!")
            return
        
        user_function = exec_globals[actual_func_name]
        
        # Professional Test Results Display
        st.markdown("""
        <div class="pro-card">
            <h3 style="color: #333; margin: 0 0 1rem 0;">🧪 Test Results</h3>
        </div>
        """, unsafe_allow_html=True)

        all_passed = True
        for i, test_case in enumerate(exercise['test_cases']):
            try:
                # Handle different input formats
                if isinstance(test_case['input'], tuple):
                    result = user_function(*test_case['input'])
                else:
                    result = user_function(test_case['input'])

                # Check if expected content is in result
                passed = all(expected in str(result) for expected in test_case['expected_contains'])

                if passed:
                    st.markdown(f"""
                    <div class="success-message">
                        <strong>✅ Test {i+1}: PASSED</strong>
                    </div>
                    """, unsafe_allow_html=True)
                    st.code(f"Input: {test_case['input']}\nOutput: {result}", language="python")
                else:
                    st.markdown(f"""
                    <div class="error-message">
                        <strong>❌ Test {i+1}: FAILED</strong>
                    </div>
                    """, unsafe_allow_html=True)
                    st.code(f"Input: {test_case['input']}\nOutput: {result}\nExpected to contain: {test_case['expected_contains']}", language="python")
                    all_passed = False

            except Exception as e:
                st.markdown(f"""
                <div class="error-message">
                    <strong>❌ Test {i+1}: ERROR - {str(e)}</strong>
                </div>
                """, unsafe_allow_html=True)
                all_passed = False

        if all_passed:
            st.markdown("""
            <div style="background: linear-gradient(135deg, #4caf50, #8bc34a); color: white; padding: 2rem; border-radius: 15px; text-align: center; margin: 2rem 0;">
                <h2 style="margin: 0 0 0.5rem 0; color: white;">🎉 Congratulations!</h2>
                <p style="margin: 0; font-size: 1.2rem; opacity: 0.9;">All tests passed! You've successfully completed this exercise!</p>
            </div>
            """, unsafe_allow_html=True)

            st.session_state.user_progress['successful_attempts'] += 1
            st.session_state.user_progress['tools_created'] += 1
            st.session_state.completed_exercises.add(exercise['id'])

            # Add to history
            st.session_state.practice_history.append({
                'exercise_id': exercise['id'],
                'timestamp': datetime.now(),
                'success': True,
                'code': user_code
            })

            st.balloons()
        else:
            st.markdown("""
            <div style="background: linear-gradient(135deg, #ff9800, #ffb74d); color: white; padding: 1.5rem; border-radius: 10px; text-align: center; margin: 1rem 0;">
                <h4 style="margin: 0 0 0.5rem 0; color: white;">💪 Keep Going!</h4>
                <p style="margin: 0; opacity: 0.9;">Some tests failed, but you're on the right track. Review the feedback and try again!</p>
            </div>
            """, unsafe_allow_html=True)
            
    except Exception as e:
        st.error(f"❌ Code execution error: {str(e)}")
        st.code(traceback.format_exc())

def show_hint(exercise):
    """Show hints for the exercise with professional styling"""
    hints = {
        'basic_1': "💡 Use f-strings to format the greeting message with the user's name.",
        'basic_2': "💡 Use if-elif statements to handle different operations. Don't forget to check for division by zero!",
        'basic_3': "💡 Use len() for characters, split() for words, and count() for sentences.",
        'inter_1': "💡 Import the 're' module and use regular expressions for validation patterns."
    }

    hint = hints.get(exercise['id'], "💡 Think about the problem step by step. Break it down into smaller parts.")

    st.markdown(f"""
    <div style="background: linear-gradient(135deg, #2196f3, #64b5f6); color: white; padding: 1.5rem; border-radius: 10px; margin: 1rem 0;">
        <h4 style="margin: 0 0 0.5rem 0; color: white;">💡 Helpful Hint</h4>
        <p style="margin: 0; font-size: 1.1rem; opacity: 0.95;">{hint}</p>
    </div>
    """, unsafe_allow_html=True)

def show_solution(exercise):
    """Show the solution for the exercise with professional styling"""
    st.markdown("""
    <div style="background: linear-gradient(135deg, #4caf50, #8bc34a); color: white; padding: 1rem; border-radius: 10px; margin: 1rem 0;">
        <h4 style="margin: 0; color: white;">✅ Complete Solution</h4>
    </div>
    """, unsafe_allow_html=True)

    with st.expander("View Solution Code", expanded=True):
        st.code(exercise['solution'], language='python')

        st.markdown("""
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
            <strong>⚠️ Learning Tip:</strong> Try to solve it yourself first! Looking at solutions too early can hinder your learning process.
            Use this as a reference to understand the approach and improve your own solution.
        </div>
        """, unsafe_allow_html=True)

def teaching_mode():
    """Teaching mode for educators and peer learning"""

    st.header("👩‍🏫 Teaching Mode")
    st.markdown("**Perfect for classroom demonstrations and peer learning sessions**")

    # Teaching tools
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📋 Lesson Planner")

        # Lesson selection
        lesson_type = st.selectbox(
            "Choose Lesson Type:",
            [
                "Introduction to Function Tools",
                "Basic Tool Creation",
                "Error Handling in Tools",
                "Advanced Tool Patterns",
                "Multi-Agent Coordination",
                "Custom Lesson"
            ]
        )

        if lesson_type == "Custom Lesson":
            custom_topic = st.text_input("Enter custom topic:")
            if custom_topic:
                generate_lesson_plan(custom_topic)
        else:
            generate_lesson_plan(lesson_type)

    with col2:
        st.subheader("👥 Class Management")

        # Student progress simulation
        if st.button("📊 Show Class Progress"):
            show_class_progress()

        # Live coding session
        if st.button("💻 Start Live Coding"):
            live_coding_session()

        # Assignment generator
        if st.button("📝 Generate Assignment"):
            generate_assignment()

    # Interactive demonstration area
    st.subheader("🎭 Interactive Demonstration")

    demo_exercise = st.selectbox(
        "Select Exercise for Demo:",
        ["Basic Calculator", "Text Processor", "Data Validator", "JSON Processor"]
    )

    if st.button("🚀 Start Demo"):
        run_demo_exercise(demo_exercise)

    # Teaching Mode Footer with Panaversity Credits
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; padding: 15px; background-color: #e8f4fd; border-radius: 10px; margin-top: 20px;'>
        <h6 style='color: #1f77b4; margin: 0 0 8px 0;'>👩‍🏫 Teaching Resources Inspired by Panaversity</h6>
        <p style='margin: 0; font-size: 12px; color: #666;'>
            Teaching methodologies and content structure based on
            <a href='https://panaversity.org/' target='_blank' style='color: #1f77b4;'>Panaversity</a>'s educational approach
        </p>
    </div>
    """, unsafe_allow_html=True)

def analytics_dashboard():
    """Analytics dashboard for progress tracking"""

    st.header("📊 Analytics Dashboard")
    st.markdown("**Track learning progress and performance metrics**")

    # Overall statistics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        total_exercises = sum(len(level['exercises']) for level in PRACTICE_EXERCISES.values())
        st.metric("Total Exercises", total_exercises)

    with col2:
        completed = len(st.session_state.completed_exercises)
        st.metric("Completed", completed)

    with col3:
        progress = st.session_state.user_progress
        success_rate = (progress['successful_attempts'] / max(progress['total_attempts'], 1)) * 100
        st.metric("Success Rate", f"{success_rate:.1f}%")

    with col4:
        session_time = datetime.now() - st.session_state.user_progress['session_start']
        st.metric("Session Time", Formatter.format_time(session_time.total_seconds()))

    # Progress visualization
    st.subheader("📈 Progress Visualization")

    # Create progress data
    levels = ['Basic', 'Intermediate', 'Advanced', 'Expert']
    progress_data = []

    for level in levels:
        if level in PRACTICE_EXERCISES:
            total = len(PRACTICE_EXERCISES[level]['exercises'])
            completed = sum(1 for ex in PRACTICE_EXERCISES[level]['exercises']
                          if ex['id'] in st.session_state.completed_exercises)
            progress_data.append({
                'Level': level,
                'Completed': completed,
                'Total': total,
                'Progress': (completed / total) * 100 if total > 0 else 0
            })

    # Display progress chart
    if progress_data:
        import pandas as pd
        df = pd.DataFrame(progress_data)
        st.bar_chart(df.set_index('Level')['Progress'])

    # Detailed history
    st.subheader("📝 Practice History")

    if st.session_state.practice_history:
        for i, entry in enumerate(reversed(st.session_state.practice_history[-10:])):
            with st.expander(f"Exercise {entry['exercise_id']} - {entry['timestamp'].strftime('%H:%M:%S')}"):
                st.write(f"**Success:** {'✅' if entry['success'] else '❌'}")
                st.code(entry['code'], language='python')
    else:
        st.info("No practice history yet. Start practicing to see your progress!")

    # Analytics Dashboard Footer with Panaversity Credits
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; padding: 15px; background-color: #f0f8f0; border-radius: 10px; margin-top: 20px;'>
        <h6 style='color: #28a745; margin: 0 0 8px 0;'>📊 Analytics Framework Inspired by Panaversity</h6>
        <p style='margin: 0; font-size: 12px; color: #666;'>
            Learning analytics and progress tracking concepts learned from
            <a href='https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first' target='_blank' style='color: #28a745;'>Panaversity's AI Agents Course</a>
        </p>
    </div>
    """, unsafe_allow_html=True)

def generate_lesson_plan(topic):
    """Generate a lesson plan for the given topic"""

    lesson_plans = {
        "Introduction to Function Tools": {
            "duration": "30 minutes",
            "objectives": [
                "Understand what function tools are",
                "Learn the @function_tool decorator",
                "Create a simple function tool"
            ],
            "activities": [
                "Demo: Show existing function tools",
                "Explanation: Decorator pattern",
                "Practice: Create greeting tool",
                "Discussion: Use cases"
            ]
        },
        "Basic Tool Creation": {
            "duration": "45 minutes",
            "objectives": [
                "Master basic function tool syntax",
                "Implement error handling",
                "Test function tools"
            ],
            "activities": [
                "Review: Function tool basics",
                "Practice: Calculator tool",
                "Exercise: Text counter",
                "Testing: Validate solutions"
            ]
        },
        "Error Handling in Tools": {
            "duration": "40 minutes",
            "objectives": [
                "Implement robust error handling",
                "Provide user-friendly error messages",
                "Handle edge cases"
            ],
            "activities": [
                "Demo: Common errors",
                "Practice: Add error handling",
                "Exercise: Data validator",
                "Review: Best practices"
            ]
        }
    }

    if topic in lesson_plans:
        plan = lesson_plans[topic]

        st.success(f"📋 Lesson Plan: {topic}")
        st.write(f"**Duration:** {plan['duration']}")

        st.write("**Learning Objectives:**")
        for obj in plan['objectives']:
            st.write(f"• {obj}")

        st.write("**Activities:**")
        for activity in plan['activities']:
            st.write(f"• {activity}")
    else:
        st.info(f"📋 Custom lesson plan for: {topic}")
        st.write("**Suggested Structure:**")
        st.write("• Introduction (5 min)")
        st.write("• Demonstration (10 min)")
        st.write("• Hands-on Practice (20 min)")
        st.write("• Review and Q&A (10 min)")

def show_class_progress():
    """Show simulated class progress for teaching demo"""

    st.subheader("👥 Simulated Class Progress")

    # Generate fake student data for demo
    students = [
        {"name": "Alice", "completed": 8, "success_rate": 92},
        {"name": "Bob", "completed": 6, "success_rate": 78},
        {"name": "Charlie", "completed": 10, "success_rate": 95},
        {"name": "Diana", "completed": 4, "success_rate": 85},
        {"name": "Eve", "completed": 7, "success_rate": 88}
    ]

    for student in students:
        col1, col2, col3 = st.columns(3)
        with col1:
            st.write(f"👤 {student['name']}")
        with col2:
            st.write(f"📊 {student['completed']} exercises")
        with col3:
            st.write(f"✅ {student['success_rate']}% success")

def live_coding_session():
    """Start a live coding demonstration"""

    st.subheader("💻 Live Coding Session")
    st.info("Perfect for real-time demonstrations and collaborative coding!")

    # Code editor for live demo
    demo_code = st.text_area(
        "Live Coding Area (share your screen for class demo):",
        value='''@function_tool("demo_tool")
def demo_tool(input_data: str) -> str:
    """
    Demonstration tool for live coding.
    """
    # Write code here during the demo
    return f"Demo result: {input_data}"''',
        height=200
    )

    if st.button("🧪 Test Live Code"):
        st.code(demo_code, language='python')
        st.success("✅ Code is ready for demonstration!")

def generate_assignment():
    """Generate practice assignments for students"""

    st.subheader("📝 Assignment Generator")

    assignment_types = {
        "Homework Assignment": [
            "Create a temperature converter tool",
            "Build a password strength checker",
            "Implement a word frequency counter"
        ],
        "Lab Exercise": [
            "Design a data analysis toolkit",
            "Create a file processing system",
            "Build a multi-function calculator"
        ],
        "Project Assignment": [
            "Develop a complete AI agent tool suite",
            "Create an educational game using function tools",
            "Build a data visualization toolkit"
        ]
    }

    assignment_type = st.selectbox("Assignment Type:", list(assignment_types.keys()))

    if assignment_type:
        st.write(f"**{assignment_type} Options:**")
        for i, assignment in enumerate(assignment_types[assignment_type], 1):
            st.write(f"{i}. {assignment}")

def run_demo_exercise(exercise_name):
    """Run a demonstration exercise"""

    st.subheader(f"🎭 Demo: {exercise_name}")

    demo_exercises = {
        "Basic Calculator": {
            "code": '''@function_tool("demo_calculator")
def demo_calculator(a: float, b: float, op: str) -> str:
    if op == '+':
        return f"{a} + {b} = {a + b}"
    elif op == '-':
        return f"{a} - {b} = {a - b}"
    elif op == '*':
        return f"{a} * {b} = {a * b}"
    elif op == '/':
        return f"{a} / {b} = {a / b}" if b != 0 else "Error: Division by zero"
    else:
        return f"Unknown operation: {op}"''',
            "test": "demo_calculator(10, 5, '+')"
        },
        "Text Processor": {
            "code": '''@function_tool("demo_text_processor")
def demo_text_processor(text: str) -> str:
    words = len(text.split())
    chars = len(text)
    sentences = text.count('.') + text.count('!') + text.count('?')
    return f"Words: {words}, Characters: {chars}, Sentences: {sentences}"''',
            "test": "demo_text_processor('Hello world! How are you?')"
        }
    }

    if exercise_name in demo_exercises:
        demo = demo_exercises[exercise_name]
        st.code(demo["code"], language='python')
        st.write(f"**Test:** `{demo['test']}`")
        st.success("✅ Demo ready for presentation!")

if __name__ == "__main__":
    main()
