"""
Interactive AI Agents Practice Interface
=======================================

A comprehensive Streamlit-based practice interface for learning and teaching
AI agent function tools from basic to advanced levels.

Repository: https://github.com/asadullah48/tool.git
Author: asadullah48
License: MIT

Learning Source: Panaversity - Learn Agentic AI
Course: https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first
Website: https://panaversity.org/

Features:
- Progressive learning levels (Basic → Intermediate → Advanced → Expert)
- Interactive code editor with real-time execution
- Visual feedback and explanations
- Progress tracking and achievements
- Teaching mode for classroom use
- Peer learning activities

Usage:
    streamlit run practices.py
"""

import streamlit as st
import asyncio
import sys
import time
import json
from datetime import datetime
from pathlib import Path
import traceback

# Import our AI agents system
try:
    from config import config, logger
    from main import agent_manager, run_single_query
    from agents.tool import function_tool
    from tools_collection import track_tool_usage, explain_tool
    from utils import Formatter, performance_monitor
except ImportError as e:
    st.error(f"❌ Import Error: {e}")
    st.error("Please ensure all dependencies are installed and the project is properly configured.")
    st.stop()

# Configure Streamlit page
st.set_page_config(
    page_title="AI Agents Practice Lab",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'practice_level' not in st.session_state:
    st.session_state.practice_level = 'Basic'
if 'completed_exercises' not in st.session_state:
    st.session_state.completed_exercises = set()
if 'user_progress' not in st.session_state:
    st.session_state.user_progress = {
        'total_attempts': 0,
        'successful_attempts': 0,
        'tools_created': 0,
        'session_start': datetime.now()
    }
if 'practice_history' not in st.session_state:
    st.session_state.practice_history = []
if 'show_welcome' not in st.session_state:
    st.session_state.show_welcome = True

# Practice exercises database
PRACTICE_EXERCISES = {
    'Basic': {
        'title': '🟢 Basic Level - Function Tool Fundamentals',
        'description': 'Learn the basics of creating function tools for AI agents',
        'exercises': [
            {
                'id': 'basic_1',
                'title': 'Simple Greeting Tool',
                'description': 'Create a function tool that greets users by name',
                'instruction': '''Create a function tool called "greet_user" that:
1. Takes a name parameter (string)
2. Returns a greeting message
3. Uses the @function_tool decorator''',
                'template': '''@function_tool("greet_user")
def greet_user(name: str) -> str:
    """
    Greet a user by name.
    
    Args:
        name: The user's name
    
    Returns:
        A greeting message
    """
    # Your code here
    pass''',
                'solution': '''@function_tool("greet_user")
def greet_user(name: str) -> str:
    """
    Greet a user by name.
    
    Args:
        name: The user's name
    
    Returns:
        A greeting message
    """
    return f"Hello, {name}! Welcome to AI Agents Practice Lab! 👋"''',
                'test_cases': [
                    {'input': 'Alice', 'expected_contains': ['Hello', 'Alice']},
                    {'input': 'Bob', 'expected_contains': ['Hello', 'Bob']}
                ]
            },
            {
                'id': 'basic_2',
                'title': 'Simple Calculator Tool',
                'description': 'Create a basic arithmetic calculator tool',
                'instruction': '''Create a function tool called "basic_calculator" that:
1. Takes two numbers and an operation (+, -, *, /)
2. Performs the calculation
3. Returns the result with proper formatting''',
                'template': '''@function_tool("basic_calculator")
def basic_calculator(num1: float, num2: float, operation: str) -> str:
    """
    Perform basic arithmetic operations.
    
    Args:
        num1: First number
        num2: Second number
        operation: Operation (+, -, *, /)
    
    Returns:
        Calculation result
    """
    # Your code here
    pass''',
                'solution': '''@function_tool("basic_calculator")
def basic_calculator(num1: float, num2: float, operation: str) -> str:
    """
    Perform basic arithmetic operations.
    
    Args:
        num1: First number
        num2: Second number
        operation: Operation (+, -, *, /)
    
    Returns:
        Calculation result
    """
    try:
        if operation == '+':
            result = num1 + num2
        elif operation == '-':
            result = num1 - num2
        elif operation == '*':
            result = num1 * num2
        elif operation == '/':
            if num2 == 0:
                return "❌ Error: Division by zero is not allowed"
            result = num1 / num2
        else:
            return f"❌ Error: Unknown operation '{operation}'"
        
        return f"🧮 {num1} {operation} {num2} = {result}"
    except Exception as e:
        return f"❌ Error: {str(e)}"''',
                'test_cases': [
                    {'input': (10, 5, '+'), 'expected_contains': ['15']},
                    {'input': (10, 2, '*'), 'expected_contains': ['20']},
                    {'input': (10, 0, '/'), 'expected_contains': ['Error', 'zero']}
                ]
            },
            {
                'id': 'basic_3',
                'title': 'Text Length Counter',
                'description': 'Create a tool that counts characters and words in text',
                'instruction': '''Create a function tool called "text_counter" that:
1. Takes a text string as input
2. Counts characters, words, and sentences
3. Returns a formatted summary''',
                'template': '''@function_tool("text_counter")
def text_counter(text: str) -> str:
    """
    Count characters, words, and sentences in text.
    
    Args:
        text: Input text to analyze
    
    Returns:
        Text analysis summary
    """
    # Your code here
    pass''',
                'solution': '''@function_tool("text_counter")
def text_counter(text: str) -> str:
    """
    Count characters, words, and sentences in text.
    
    Args:
        text: Input text to analyze
    
    Returns:
        Text analysis summary
    """
    char_count = len(text)
    word_count = len(text.split())
    sentence_count = text.count('.') + text.count('!') + text.count('?')
    
    return (
        f"📝 Text Analysis:\\n"
        f"🔤 Characters: {char_count}\\n"
        f"📝 Words: {word_count}\\n"
        f"📄 Sentences: {sentence_count}"
    )''',
                'test_cases': [
                    {'input': 'Hello world!', 'expected_contains': ['Characters: 12', 'Words: 2']},
                    {'input': 'AI is amazing. It helps us learn.', 'expected_contains': ['Sentences: 2']}
                ]
            }
        ]
    },
    'Intermediate': {
        'title': '🟡 Intermediate Level - Enhanced Function Tools',
        'description': 'Build more sophisticated tools with error handling and validation',
        'exercises': [
            {
                'id': 'inter_1',
                'title': 'Data Validator Tool',
                'description': 'Create a tool that validates different types of data',
                'instruction': '''Create a function tool called "data_validator" that:
1. Takes data and data_type parameters
2. Validates email, phone, or URL formats
3. Returns validation results with helpful messages''',
                'template': '''@function_tool("data_validator")
def data_validator(data: str, data_type: str) -> str:
    """
    Validate different types of data.
    
    Args:
        data: Data to validate
        data_type: Type of validation (email, phone, url)
    
    Returns:
        Validation result
    """
    import re
    # Your code here
    pass''',
                'solution': '''@function_tool("data_validator")
def data_validator(data: str, data_type: str) -> str:
    """
    Validate different types of data.
    
    Args:
        data: Data to validate
        data_type: Type of validation (email, phone, url)
    
    Returns:
        Validation result
    """
    import re
    
    if data_type.lower() == 'email':
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$'
        is_valid = re.match(pattern, data) is not None
        return f"📧 Email '{data}' is {'✅ valid' if is_valid else '❌ invalid'}"
    
    elif data_type.lower() == 'phone':
        pattern = r'^\\+?[1-9]\\d{1,14}$'
        clean_phone = re.sub(r'[\\s\\-\\(\\)]', '', data)
        is_valid = re.match(pattern, clean_phone) is not None
        return f"📱 Phone '{data}' is {'✅ valid' if is_valid else '❌ invalid'}"
    
    elif data_type.lower() == 'url':
        pattern = r'^https?://(?:[-\\w.])+(?:\\:[0-9]+)?(?:/(?:[\\w/_.])*(?:\\?(?:[\\w&=%.])*)?(?:\\#(?:[\\w.])*)?)?$'
        is_valid = re.match(pattern, data) is not None
        return f"🌐 URL '{data}' is {'✅ valid' if is_valid else '❌ invalid'}"
    
    else:
        return f"❌ Unknown data type: {data_type}. Use 'email', 'phone', or 'url'"''',
                'test_cases': [
                    {'input': ('<EMAIL>', 'email'), 'expected_contains': ['valid']},
                    {'input': ('invalid-email', 'email'), 'expected_contains': ['invalid']},
                    {'input': ('https://example.com', 'url'), 'expected_contains': ['valid']}
                ]
            },
            {
                'id': 'inter_2',
                'title': 'Smart List Processor',
                'description': 'Create a tool that processes lists with multiple operations',
                'instruction': '''Create a function tool called "list_processor" that:
1. Takes a list of numbers and an operation type
2. Supports operations: sum, average, max, min, sort
3. Returns formatted results with statistics''',
                'template': '''@function_tool("list_processor")
def list_processor(numbers: list, operation: str) -> str:
    """
    Process a list of numbers with various operations.

    Args:
        numbers: List of numbers to process
        operation: Operation type (sum, average, max, min, sort)

    Returns:
        Processed result with formatting
    """
    # Your code here
    pass''',
                'solution': '''@function_tool("list_processor")
def list_processor(numbers: list, operation: str) -> str:
    """
    Process a list of numbers with various operations.

    Args:
        numbers: List of numbers to process
        operation: Operation type (sum, average, max, min, sort)

    Returns:
        Processed result with formatting
    """
    if not numbers:
        return "❌ Error: Empty list provided"

    try:
        # Convert to numbers if needed
        nums = [float(x) for x in numbers]

        if operation.lower() == 'sum':
            result = sum(nums)
            return f"➕ Sum of {nums}: {result}"
        elif operation.lower() == 'average':
            result = sum(nums) / len(nums)
            return f"📊 Average of {nums}: {result:.2f}"
        elif operation.lower() == 'max':
            result = max(nums)
            return f"⬆️ Maximum of {nums}: {result}"
        elif operation.lower() == 'min':
            result = min(nums)
            return f"⬇️ Minimum of {nums}: {result}"
        elif operation.lower() == 'sort':
            result = sorted(nums)
            return f"🔢 Sorted {nums}: {result}"
        else:
            return f"❌ Unknown operation: {operation}. Use: sum, average, max, min, sort"
    except ValueError:
        return "❌ Error: All items must be numbers"
    except Exception as e:
        return f"❌ Error: {str(e)}"''',
                'test_cases': [
                    {'input': ([1, 2, 3, 4, 5], 'sum'), 'expected_contains': ['15']},
                    {'input': ([10, 20, 30], 'average'), 'expected_contains': ['20']},
                    {'input': ([3, 1, 4, 1, 5], 'sort'), 'expected_contains': ['[1, 1, 3, 4, 5]']}
                ]
            }
        ]
    },
    'Advanced': {
        'title': '🟠 Advanced Level - Complex Function Tools',
        'description': 'Build sophisticated tools with advanced features and integrations',
        'exercises': [
            {
                'id': 'adv_1',
                'title': 'JSON Data Processor',
                'description': 'Create a tool that processes and analyzes JSON data',
                'instruction': '''Create a function tool called "json_processor" that:
1. Takes JSON string and operation type
2. Supports: validate, extract_keys, count_items, find_value
3. Handles errors gracefully and provides detailed feedback''',
                'template': '''@function_tool("json_processor")
def json_processor(json_data: str, operation: str, key: str = "") -> str:
    """
    Process and analyze JSON data.

    Args:
        json_data: JSON string to process
        operation: Operation type (validate, extract_keys, count_items, find_value)
        key: Key to search for (used with find_value operation)

    Returns:
        Processing result
    """
    import json
    # Your code here
    pass''',
                'solution': '''@function_tool("json_processor")
def json_processor(json_data: str, operation: str, key: str = "") -> str:
    """
    Process and analyze JSON data.

    Args:
        json_data: JSON string to process
        operation: Operation type (validate, extract_keys, count_items, find_value)
        key: Key to search for (used with find_value operation)

    Returns:
        Processing result
    """
    import json

    try:
        # Parse JSON
        data = json.loads(json_data)

        if operation == 'validate':
            return "✅ JSON is valid and properly formatted"

        elif operation == 'extract_keys':
            if isinstance(data, dict):
                keys = list(data.keys())
                return f"🔑 Keys found: {keys}"
            else:
                return "❌ JSON is not an object (no keys to extract)"

        elif operation == 'count_items':
            if isinstance(data, dict):
                count = len(data)
                return f"📊 Object has {count} key-value pairs"
            elif isinstance(data, list):
                count = len(data)
                return f"📊 Array has {count} items"
            else:
                return "📊 Single value (not countable)"

        elif operation == 'find_value':
            if not key:
                return "❌ Key parameter required for find_value operation"
            if isinstance(data, dict) and key in data:
                return f"🎯 Found '{key}': {data[key]}"
            else:
                return f"❌ Key '{key}' not found in JSON"

        else:
            return f"❌ Unknown operation: {operation}"

    except json.JSONDecodeError as e:
        return f"❌ Invalid JSON: {str(e)}"
    except Exception as e:
        return f"❌ Error: {str(e)}"''',
                'test_cases': [
                    {'input': ('{"name": "Alice", "age": 30}', 'validate'), 'expected_contains': ['valid']},
                    {'input': ('{"name": "Alice", "age": 30}', 'extract_keys'), 'expected_contains': ['name', 'age']},
                    {'input': ('{"name": "Alice", "age": 30}', 'find_value', 'name'), 'expected_contains': ['Alice']}
                ]
            },
            {
                'id': 'adv_2',
                'title': 'Performance Monitor Tool',
                'description': 'Create a tool that monitors and reports function performance',
                'instruction': '''Create a function tool called "performance_monitor" that:
1. Takes a function name and execution time
2. Tracks performance metrics over multiple calls
3. Provides performance analysis and recommendations''',
                'template': '''@function_tool("performance_monitor")
def performance_monitor(func_name: str, execution_time: float, action: str = "record") -> str:
    """
    Monitor and analyze function performance.

    Args:
        func_name: Name of the function being monitored
        execution_time: Execution time in seconds
        action: Action to perform (record, report, reset)

    Returns:
        Performance analysis result
    """
    # Use a global dictionary to store performance data
    if not hasattr(performance_monitor, 'metrics'):
        performance_monitor.metrics = {}

    # Your code here
    pass''',
                'solution': '''@function_tool("performance_monitor")
def performance_monitor(func_name: str, execution_time: float, action: str = "record") -> str:
    """
    Monitor and analyze function performance.

    Args:
        func_name: Name of the function being monitored
        execution_time: Execution time in seconds
        action: Action to perform (record, report, reset)

    Returns:
        Performance analysis result
    """
    # Use a global dictionary to store performance data
    if not hasattr(performance_monitor, 'metrics'):
        performance_monitor.metrics = {}

    if action == "record":
        if func_name not in performance_monitor.metrics:
            performance_monitor.metrics[func_name] = {
                'calls': 0,
                'total_time': 0,
                'min_time': float('inf'),
                'max_time': 0,
                'times': []
            }

        metric = performance_monitor.metrics[func_name]
        metric['calls'] += 1
        metric['total_time'] += execution_time
        metric['min_time'] = min(metric['min_time'], execution_time)
        metric['max_time'] = max(metric['max_time'], execution_time)
        metric['times'].append(execution_time)

        avg_time = metric['total_time'] / metric['calls']

        return f"📊 Recorded {func_name}: {execution_time:.3f}s (avg: {avg_time:.3f}s, calls: {metric['calls']})"

    elif action == "report":
        if func_name not in performance_monitor.metrics:
            return f"❌ No performance data for '{func_name}'"

        metric = performance_monitor.metrics[func_name]
        avg_time = metric['total_time'] / metric['calls']

        # Performance assessment
        if avg_time < 0.1:
            assessment = "🟢 Excellent performance"
        elif avg_time < 0.5:
            assessment = "🟡 Good performance"
        elif avg_time < 1.0:
            assessment = "🟠 Moderate performance"
        else:
            assessment = "🔴 Needs optimization"

        return (
            f"📈 Performance Report for '{func_name}':\\n"
            f"🔢 Total Calls: {metric['calls']}\\n"
            f"⏱️ Average Time: {avg_time:.3f}s\\n"
            f"⚡ Fastest: {metric['min_time']:.3f}s\\n"
            f"🐌 Slowest: {metric['max_time']:.3f}s\\n"
            f"📊 Assessment: {assessment}"
        )

    elif action == "reset":
        if func_name in performance_monitor.metrics:
            del performance_monitor.metrics[func_name]
            return f"🔄 Reset performance data for '{func_name}'"
        else:
            return f"❌ No performance data to reset for '{func_name}'"

    else:
        return f"❌ Unknown action: {action}. Use: record, report, reset"''',
                'test_cases': [
                    {'input': ('test_func', 0.5, 'record'), 'expected_contains': ['Recorded', 'test_func', '0.500s']},
                    {'input': ('test_func', 0.3, 'record'), 'expected_contains': ['calls: 2']},
                    {'input': ('test_func', 0, 'report'), 'expected_contains': ['Performance Report', 'Average Time']}
                ]
            }
        ]
    },
    'Expert': {
        'title': '🔴 Expert Level - Advanced AI Integration',
        'description': 'Master-level tools with AI agent integration and complex workflows',
        'exercises': [
            {
                'id': 'expert_1',
                'title': 'Multi-Agent Coordinator',
                'description': 'Create a tool that coordinates multiple AI agents for complex tasks',
                'instruction': '''Create a function tool called "agent_coordinator" that:
1. Takes a task description and agent types
2. Breaks down the task for different agents
3. Coordinates the workflow and combines results''',
                'template': '''@function_tool("agent_coordinator")
def agent_coordinator(task: str, agent_types: list, coordination_mode: str = "sequential") -> str:
    """
    Coordinate multiple AI agents for complex tasks.

    Args:
        task: Description of the task to accomplish
        agent_types: List of agent types to coordinate
        coordination_mode: How to coordinate (sequential, parallel, hierarchical)

    Returns:
        Coordination plan and workflow
    """
    # Your code here
    pass''',
                'solution': '''@function_tool("agent_coordinator")
def agent_coordinator(task: str, agent_types: list, coordination_mode: str = "sequential") -> str:
    """
    Coordinate multiple AI agents for complex tasks.

    Args:
        task: Description of the task to accomplish
        agent_types: List of agent types to coordinate
        coordination_mode: How to coordinate (sequential, parallel, hierarchical)

    Returns:
        Coordination plan and workflow
    """
    if not agent_types:
        return "❌ Error: No agent types provided"

    # Define agent capabilities
    agent_capabilities = {
        'researcher': 'Information gathering and analysis',
        'technical': 'Technical implementation and code',
        'teacher': 'Educational content and explanations',
        'analyst': 'Data analysis and insights',
        'creative': 'Creative solutions and brainstorming',
        'tutor': 'Personalized guidance and feedback'
    }

    # Create coordination plan
    plan = f"🤝 Multi-Agent Coordination Plan\\n"
    plan += f"📋 Task: {task}\\n"
    plan += f"🔄 Mode: {coordination_mode}\\n"
    plan += f"👥 Agents: {', '.join(agent_types)}\\n\\n"

    if coordination_mode == "sequential":
        plan += "📊 Sequential Workflow:\\n"
        for i, agent in enumerate(agent_types, 1):
            capability = agent_capabilities.get(agent, 'General assistance')
            plan += f"  {i}. {agent.title()} Agent: {capability}\\n"

    elif coordination_mode == "parallel":
        plan += "⚡ Parallel Workflow:\\n"
        for agent in agent_types:
            capability = agent_capabilities.get(agent, 'General assistance')
            plan += f"  • {agent.title()} Agent: {capability}\\n"

    elif coordination_mode == "hierarchical":
        plan += "🏗️ Hierarchical Workflow:\\n"
        if 'researcher' in agent_types:
            plan += "  1. Researcher Agent: Initial analysis\\n"
        if 'technical' in agent_types:
            plan += "  2. Technical Agent: Implementation\\n"
        if 'teacher' in agent_types:
            plan += "  3. Teacher Agent: Documentation\\n"

    plan += f"\\n✅ Coordination plan ready for execution!"
    return plan''',
                'test_cases': [
                    {'input': ('Analyze data', ['researcher', 'analyst'], 'sequential'), 'expected_contains': ['Sequential Workflow', 'researcher', 'analyst']},
                    {'input': ('Create tutorial', ['teacher', 'creative'], 'parallel'), 'expected_contains': ['Parallel Workflow']},
                    {'input': ('Complex project', ['researcher', 'technical', 'teacher'], 'hierarchical'), 'expected_contains': ['Hierarchical Workflow']}
                ]
            }
        ]
    }
}

def main():
    """Main Streamlit application"""

    # Header with Panaversity Credits
    st.title("🤖 AI Agents Practice Lab")
    st.markdown("**Learn and teach function tools from basic to advanced levels**")

    # Welcome message for first-time users
    if st.session_state.show_welcome:
        st.markdown("---")
        st.markdown("""
        <div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 15px; margin: 15px 0; color: white; text-align: center;'>
            <h3 style='margin: 0 0 15px 0; color: white;'>🎉 Welcome to AI Agents Practice Lab!</h3>
            <p style='margin: 0 0 10px 0; font-size: 16px;'>
                This interactive learning platform is built based on concepts from
                <strong><a href='https://panaversity.org/' target='_blank' style='color: #ffd700; text-decoration: none;'>Panaversity's Learn Agentic AI</a></strong> course
            </p>
            <p style='margin: 0; font-size: 14px; opacity: 0.9;'>
                📚 <a href='https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first' target='_blank' style='color: #ffd700;'>Course Materials</a> |
                🌐 <a href='https://panaversity.org/' target='_blank' style='color: #ffd700;'>Panaversity.org</a>
            </p>
        </div>
        """, unsafe_allow_html=True)

        col1, col2, col3 = st.columns([1, 1, 1])
        with col2:
            if st.button("🚀 Start Learning!", key="start_learning"):
                st.session_state.show_welcome = False
                st.rerun()
        st.markdown("---")
    else:
        # Compact Panaversity Credits Section
        st.markdown("---")
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            st.markdown("""
            <div style='text-align: center; padding: 8px; background-color: #f0f2f6; border-radius: 8px; margin: 8px 0;'>
                <p style='margin: 0; font-size: 12px; color: #666;'>
                    🎓 Built with concepts from <a href='https://panaversity.org/' target='_blank' style='color: #1f77b4;'><strong>Panaversity's Learn Agentic AI</strong></a> course
                </p>
            </div>
            """, unsafe_allow_html=True)
        st.markdown("---")

    # Mode selection
    mode = st.radio(
        "Choose Mode:",
        ["🎓 Learning Mode", "👩‍🏫 Teaching Mode", "📊 Analytics Dashboard"],
        horizontal=True
    )

    if mode == "🎓 Learning Mode":
        learning_mode()
    elif mode == "👩‍🏫 Teaching Mode":
        teaching_mode()
    elif mode == "📊 Analytics Dashboard":
        analytics_dashboard()

def learning_mode():
    """Individual learning mode"""

    # Sidebar with Credits
    with st.sidebar:
        # Panaversity Credits in Sidebar
        st.markdown("""
        <div style='background-color: #e8f4fd; padding: 15px; border-radius: 10px; margin-bottom: 20px; border-left: 4px solid #1f77b4;'>
            <h4 style='color: #1f77b4; margin: 0 0 10px 0; font-size: 16px;'>🎓 Powered by Panaversity</h4>
            <p style='margin: 0; font-size: 12px; line-height: 1.4;'>
                Learning from <strong>Panaversity's Agentic AI Course</strong><br>
                <a href='https://panaversity.org/' target='_blank' style='color: #1f77b4;'>🌐 panaversity.org</a><br>
                <a href='https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first' target='_blank' style='color: #1f77b4;'>📚 Course Materials</a>
            </p>
        </div>
        """, unsafe_allow_html=True)

        st.header("🎯 Practice Controls")
        
        # Level selection
        level = st.selectbox(
            "Choose Practice Level:",
            options=['Basic', 'Intermediate', 'Advanced', 'Expert'],
            index=['Basic', 'Intermediate', 'Advanced', 'Expert'].index(st.session_state.practice_level)
        )
        st.session_state.practice_level = level
        
        # Progress display
        st.header("📊 Your Progress")
        progress = st.session_state.user_progress
        
        col1, col2 = st.columns(2)
        with col1:
            st.metric("Attempts", progress['total_attempts'])
            st.metric("Tools Created", progress['tools_created'])
        with col2:
            success_rate = (progress['successful_attempts'] / max(progress['total_attempts'], 1)) * 100
            st.metric("Success Rate", f"{success_rate:.1f}%")
            
            session_time = datetime.now() - progress['session_start']
            st.metric("Session Time", Formatter.format_time(session_time.total_seconds()))
        
        # Completed exercises
        if st.session_state.completed_exercises:
            st.header("🏆 Completed")
            for exercise_id in st.session_state.completed_exercises:
                st.success(f"✅ {exercise_id}")
        
        # Reset progress
        if st.button("🔄 Reset Progress"):
            st.session_state.completed_exercises = set()
            st.session_state.user_progress = {
                'total_attempts': 0,
                'successful_attempts': 0,
                'tools_created': 0,
                'session_start': datetime.now()
            }
            st.session_state.practice_history = []
            st.rerun()
    
    # Main content
    if level in PRACTICE_EXERCISES:
        level_data = PRACTICE_EXERCISES[level]
        
        st.header(level_data['title'])
        st.markdown(level_data['description'])
        
        # Exercise selection
        exercise_options = [f"{ex['id']}: {ex['title']}" for ex in level_data['exercises']]
        selected_exercise = st.selectbox("Select Exercise:", exercise_options)
        
        if selected_exercise:
            exercise_id = selected_exercise.split(':')[0]
            exercise = next(ex for ex in level_data['exercises'] if ex['id'] == exercise_id)
            
            display_exercise(exercise)
    else:
        st.warning(f"Level '{level}' is coming soon! 🚧")
        st.info("Currently available: Basic and Intermediate levels")

    # Footer with Panaversity Credits
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; padding: 20px; background-color: #f8f9fa; border-radius: 10px; margin-top: 30px;'>
        <h5 style='color: #1f77b4; margin: 0 0 10px 0;'>🙏 Acknowledgments</h5>
        <p style='margin: 5px 0; font-size: 14px;'>
            This practice interface is built based on concepts learned from
            <strong><a href='https://panaversity.org/' target='_blank' style='color: #1f77b4; text-decoration: none;'>Panaversity</a></strong>'s
            <strong>Learn Agentic AI</strong> course
        </p>
        <p style='margin: 5px 0; font-size: 12px; color: #666;'>
            📚 Course Materials: <a href='https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first' target='_blank' style='color: #1f77b4;'>AI Agents First</a> |
            🌐 Website: <a href='https://panaversity.org/' target='_blank' style='color: #1f77b4;'>panaversity.org</a>
        </p>
        <p style='margin: 10px 0 0 0; font-size: 12px; color: #888;'>
            Built with ❤️ for education | Repository: <a href='https://github.com/asadullah48/tool.git' target='_blank' style='color: #1f77b4;'>asadullah48/tool</a>
        </p>
    </div>
    """, unsafe_allow_html=True)

def display_exercise(exercise):
    """Display and handle a practice exercise"""
    
    st.subheader(f"📝 {exercise['title']}")
    st.markdown(exercise['description'])
    
    # Instructions
    with st.expander("📋 Instructions", expanded=True):
        st.markdown(exercise['instruction'])
    
    # Code editor
    st.markdown("### 💻 Code Editor")
    
    # Show template or user's previous attempt
    default_code = exercise['template']
    if f"code_{exercise['id']}" in st.session_state:
        default_code = st.session_state[f"code_{exercise['id']}"]
    
    user_code = st.text_area(
        "Write your function tool:",
        value=default_code,
        height=300,
        key=f"editor_{exercise['id']}"
    )
    
    # Save code to session state
    st.session_state[f"code_{exercise['id']}"] = user_code
    
    # Action buttons
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🧪 Test Code", key=f"test_{exercise['id']}"):
            test_user_code(exercise, user_code)
    
    with col2:
        if st.button("💡 Show Hint", key=f"hint_{exercise['id']}"):
            show_hint(exercise)
    
    with col3:
        if st.button("✅ Show Solution", key=f"solution_{exercise['id']}"):
            show_solution(exercise)
    
    with col4:
        if st.button("🔄 Reset Code", key=f"reset_{exercise['id']}"):
            st.session_state[f"code_{exercise['id']}"] = exercise['template']
            st.rerun()

def test_user_code(exercise, user_code):
    """Test user's code against test cases"""
    
    st.session_state.user_progress['total_attempts'] += 1
    
    try:
        # Create a safe execution environment
        exec_globals = {
            '__builtins__': __builtins__,
            'function_tool': function_tool,
            're': __import__('re'),
            'math': __import__('math'),
            'datetime': __import__('datetime'),
            'json': __import__('json')
        }
        
        # Execute user code
        exec(user_code, exec_globals)
        
        # Extract the function name from the decorator
        import re
        func_name_match = re.search(r'@function_tool\("([^"]+)"\)', user_code)
        if not func_name_match:
            st.error("❌ No @function_tool decorator found!")
            return
        
        func_name = func_name_match.group(1)
        
        # Get the actual function
        actual_func_name = user_code.split('def ')[1].split('(')[0].strip()
        if actual_func_name not in exec_globals:
            st.error(f"❌ Function '{actual_func_name}' not found!")
            return
        
        user_function = exec_globals[actual_func_name]
        
        # Run test cases
        st.markdown("### 🧪 Test Results")
        
        all_passed = True
        for i, test_case in enumerate(exercise['test_cases']):
            try:
                # Handle different input formats
                if isinstance(test_case['input'], tuple):
                    result = user_function(*test_case['input'])
                else:
                    result = user_function(test_case['input'])
                
                # Check if expected content is in result
                passed = all(expected in str(result) for expected in test_case['expected_contains'])
                
                if passed:
                    st.success(f"✅ Test {i+1}: PASSED")
                    st.code(f"Input: {test_case['input']}\nOutput: {result}")
                else:
                    st.error(f"❌ Test {i+1}: FAILED")
                    st.code(f"Input: {test_case['input']}\nOutput: {result}\nExpected to contain: {test_case['expected_contains']}")
                    all_passed = False
                    
            except Exception as e:
                st.error(f"❌ Test {i+1}: ERROR - {str(e)}")
                all_passed = False
        
        if all_passed:
            st.success("🎉 All tests passed! Great job!")
            st.session_state.user_progress['successful_attempts'] += 1
            st.session_state.user_progress['tools_created'] += 1
            st.session_state.completed_exercises.add(exercise['id'])
            
            # Add to history
            st.session_state.practice_history.append({
                'exercise_id': exercise['id'],
                'timestamp': datetime.now(),
                'success': True,
                'code': user_code
            })
            
            st.balloons()
        else:
            st.warning("Some tests failed. Keep trying! 💪")
            
    except Exception as e:
        st.error(f"❌ Code execution error: {str(e)}")
        st.code(traceback.format_exc())

def show_hint(exercise):
    """Show hints for the exercise"""
    hints = {
        'basic_1': "💡 Use f-strings to format the greeting message with the user's name.",
        'basic_2': "💡 Use if-elif statements to handle different operations. Don't forget to check for division by zero!",
        'basic_3': "💡 Use len() for characters, split() for words, and count() for sentences.",
        'inter_1': "💡 Import the 're' module and use regular expressions for validation patterns."
    }
    
    hint = hints.get(exercise['id'], "💡 Think about the problem step by step. Break it down into smaller parts.")
    st.info(hint)

def show_solution(exercise):
    """Show the solution for the exercise"""
    with st.expander("✅ Solution Code", expanded=True):
        st.code(exercise['solution'], language='python')
        st.warning("⚠️ Try to solve it yourself first! Learning happens through practice.")

def teaching_mode():
    """Teaching mode for educators and peer learning"""

    st.header("👩‍🏫 Teaching Mode")
    st.markdown("**Perfect for classroom demonstrations and peer learning sessions**")

    # Teaching tools
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📋 Lesson Planner")

        # Lesson selection
        lesson_type = st.selectbox(
            "Choose Lesson Type:",
            [
                "Introduction to Function Tools",
                "Basic Tool Creation",
                "Error Handling in Tools",
                "Advanced Tool Patterns",
                "Multi-Agent Coordination",
                "Custom Lesson"
            ]
        )

        if lesson_type == "Custom Lesson":
            custom_topic = st.text_input("Enter custom topic:")
            if custom_topic:
                generate_lesson_plan(custom_topic)
        else:
            generate_lesson_plan(lesson_type)

    with col2:
        st.subheader("👥 Class Management")

        # Student progress simulation
        if st.button("📊 Show Class Progress"):
            show_class_progress()

        # Live coding session
        if st.button("💻 Start Live Coding"):
            live_coding_session()

        # Assignment generator
        if st.button("📝 Generate Assignment"):
            generate_assignment()

    # Interactive demonstration area
    st.subheader("🎭 Interactive Demonstration")

    demo_exercise = st.selectbox(
        "Select Exercise for Demo:",
        ["Basic Calculator", "Text Processor", "Data Validator", "JSON Processor"]
    )

    if st.button("🚀 Start Demo"):
        run_demo_exercise(demo_exercise)

    # Teaching Mode Footer with Panaversity Credits
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; padding: 15px; background-color: #e8f4fd; border-radius: 10px; margin-top: 20px;'>
        <h6 style='color: #1f77b4; margin: 0 0 8px 0;'>👩‍🏫 Teaching Resources Inspired by Panaversity</h6>
        <p style='margin: 0; font-size: 12px; color: #666;'>
            Teaching methodologies and content structure based on
            <a href='https://panaversity.org/' target='_blank' style='color: #1f77b4;'>Panaversity</a>'s educational approach
        </p>
    </div>
    """, unsafe_allow_html=True)

def analytics_dashboard():
    """Analytics dashboard for progress tracking"""

    st.header("📊 Analytics Dashboard")
    st.markdown("**Track learning progress and performance metrics**")

    # Overall statistics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        total_exercises = sum(len(level['exercises']) for level in PRACTICE_EXERCISES.values())
        st.metric("Total Exercises", total_exercises)

    with col2:
        completed = len(st.session_state.completed_exercises)
        st.metric("Completed", completed)

    with col3:
        progress = st.session_state.user_progress
        success_rate = (progress['successful_attempts'] / max(progress['total_attempts'], 1)) * 100
        st.metric("Success Rate", f"{success_rate:.1f}%")

    with col4:
        session_time = datetime.now() - st.session_state.user_progress['session_start']
        st.metric("Session Time", Formatter.format_time(session_time.total_seconds()))

    # Progress visualization
    st.subheader("📈 Progress Visualization")

    # Create progress data
    levels = ['Basic', 'Intermediate', 'Advanced', 'Expert']
    progress_data = []

    for level in levels:
        if level in PRACTICE_EXERCISES:
            total = len(PRACTICE_EXERCISES[level]['exercises'])
            completed = sum(1 for ex in PRACTICE_EXERCISES[level]['exercises']
                          if ex['id'] in st.session_state.completed_exercises)
            progress_data.append({
                'Level': level,
                'Completed': completed,
                'Total': total,
                'Progress': (completed / total) * 100 if total > 0 else 0
            })

    # Display progress chart
    if progress_data:
        import pandas as pd
        df = pd.DataFrame(progress_data)
        st.bar_chart(df.set_index('Level')['Progress'])

    # Detailed history
    st.subheader("📝 Practice History")

    if st.session_state.practice_history:
        for i, entry in enumerate(reversed(st.session_state.practice_history[-10:])):
            with st.expander(f"Exercise {entry['exercise_id']} - {entry['timestamp'].strftime('%H:%M:%S')}"):
                st.write(f"**Success:** {'✅' if entry['success'] else '❌'}")
                st.code(entry['code'], language='python')
    else:
        st.info("No practice history yet. Start practicing to see your progress!")

    # Analytics Dashboard Footer with Panaversity Credits
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; padding: 15px; background-color: #f0f8f0; border-radius: 10px; margin-top: 20px;'>
        <h6 style='color: #28a745; margin: 0 0 8px 0;'>📊 Analytics Framework Inspired by Panaversity</h6>
        <p style='margin: 0; font-size: 12px; color: #666;'>
            Learning analytics and progress tracking concepts learned from
            <a href='https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first' target='_blank' style='color: #28a745;'>Panaversity's AI Agents Course</a>
        </p>
    </div>
    """, unsafe_allow_html=True)

def generate_lesson_plan(topic):
    """Generate a lesson plan for the given topic"""

    lesson_plans = {
        "Introduction to Function Tools": {
            "duration": "30 minutes",
            "objectives": [
                "Understand what function tools are",
                "Learn the @function_tool decorator",
                "Create a simple function tool"
            ],
            "activities": [
                "Demo: Show existing function tools",
                "Explanation: Decorator pattern",
                "Practice: Create greeting tool",
                "Discussion: Use cases"
            ]
        },
        "Basic Tool Creation": {
            "duration": "45 minutes",
            "objectives": [
                "Master basic function tool syntax",
                "Implement error handling",
                "Test function tools"
            ],
            "activities": [
                "Review: Function tool basics",
                "Practice: Calculator tool",
                "Exercise: Text counter",
                "Testing: Validate solutions"
            ]
        },
        "Error Handling in Tools": {
            "duration": "40 minutes",
            "objectives": [
                "Implement robust error handling",
                "Provide user-friendly error messages",
                "Handle edge cases"
            ],
            "activities": [
                "Demo: Common errors",
                "Practice: Add error handling",
                "Exercise: Data validator",
                "Review: Best practices"
            ]
        }
    }

    if topic in lesson_plans:
        plan = lesson_plans[topic]

        st.success(f"📋 Lesson Plan: {topic}")
        st.write(f"**Duration:** {plan['duration']}")

        st.write("**Learning Objectives:**")
        for obj in plan['objectives']:
            st.write(f"• {obj}")

        st.write("**Activities:**")
        for activity in plan['activities']:
            st.write(f"• {activity}")
    else:
        st.info(f"📋 Custom lesson plan for: {topic}")
        st.write("**Suggested Structure:**")
        st.write("• Introduction (5 min)")
        st.write("• Demonstration (10 min)")
        st.write("• Hands-on Practice (20 min)")
        st.write("• Review and Q&A (10 min)")

def show_class_progress():
    """Show simulated class progress for teaching demo"""

    st.subheader("👥 Simulated Class Progress")

    # Generate fake student data for demo
    students = [
        {"name": "Alice", "completed": 8, "success_rate": 92},
        {"name": "Bob", "completed": 6, "success_rate": 78},
        {"name": "Charlie", "completed": 10, "success_rate": 95},
        {"name": "Diana", "completed": 4, "success_rate": 85},
        {"name": "Eve", "completed": 7, "success_rate": 88}
    ]

    for student in students:
        col1, col2, col3 = st.columns(3)
        with col1:
            st.write(f"👤 {student['name']}")
        with col2:
            st.write(f"📊 {student['completed']} exercises")
        with col3:
            st.write(f"✅ {student['success_rate']}% success")

def live_coding_session():
    """Start a live coding demonstration"""

    st.subheader("💻 Live Coding Session")
    st.info("Perfect for real-time demonstrations and collaborative coding!")

    # Code editor for live demo
    demo_code = st.text_area(
        "Live Coding Area (share your screen for class demo):",
        value='''@function_tool("demo_tool")
def demo_tool(input_data: str) -> str:
    """
    Demonstration tool for live coding.
    """
    # Write code here during the demo
    return f"Demo result: {input_data}"''',
        height=200
    )

    if st.button("🧪 Test Live Code"):
        st.code(demo_code, language='python')
        st.success("✅ Code is ready for demonstration!")

def generate_assignment():
    """Generate practice assignments for students"""

    st.subheader("📝 Assignment Generator")

    assignment_types = {
        "Homework Assignment": [
            "Create a temperature converter tool",
            "Build a password strength checker",
            "Implement a word frequency counter"
        ],
        "Lab Exercise": [
            "Design a data analysis toolkit",
            "Create a file processing system",
            "Build a multi-function calculator"
        ],
        "Project Assignment": [
            "Develop a complete AI agent tool suite",
            "Create an educational game using function tools",
            "Build a data visualization toolkit"
        ]
    }

    assignment_type = st.selectbox("Assignment Type:", list(assignment_types.keys()))

    if assignment_type:
        st.write(f"**{assignment_type} Options:**")
        for i, assignment in enumerate(assignment_types[assignment_type], 1):
            st.write(f"{i}. {assignment}")

def run_demo_exercise(exercise_name):
    """Run a demonstration exercise"""

    st.subheader(f"🎭 Demo: {exercise_name}")

    demo_exercises = {
        "Basic Calculator": {
            "code": '''@function_tool("demo_calculator")
def demo_calculator(a: float, b: float, op: str) -> str:
    if op == '+':
        return f"{a} + {b} = {a + b}"
    elif op == '-':
        return f"{a} - {b} = {a - b}"
    elif op == '*':
        return f"{a} * {b} = {a * b}"
    elif op == '/':
        return f"{a} / {b} = {a / b}" if b != 0 else "Error: Division by zero"
    else:
        return f"Unknown operation: {op}"''',
            "test": "demo_calculator(10, 5, '+')"
        },
        "Text Processor": {
            "code": '''@function_tool("demo_text_processor")
def demo_text_processor(text: str) -> str:
    words = len(text.split())
    chars = len(text)
    sentences = text.count('.') + text.count('!') + text.count('?')
    return f"Words: {words}, Characters: {chars}, Sentences: {sentences}"''',
            "test": "demo_text_processor('Hello world! How are you?')"
        }
    }

    if exercise_name in demo_exercises:
        demo = demo_exercises[exercise_name]
        st.code(demo["code"], language='python')
        st.write(f"**Test:** `{demo['test']}`")
        st.success("✅ Demo ready for presentation!")

if __name__ == "__main__":
    main()
