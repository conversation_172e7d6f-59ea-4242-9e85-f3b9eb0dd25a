"""
Utilities Module
===============

This module contains utility functions and classes used throughout the AI agents system.
It includes helpers for data management, formatting, validation, and common operations.
"""

import os
import json
import time
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
import logging

from config import config

# =============================================================================
# DATA MANAGEMENT UTILITIES
# =============================================================================

class DataManager:
    """Manages data storage and retrieval for the application"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
    def save_json(self, filename: str, data: Dict[str, Any]) -> bool:
        """Save data to JSON file"""
        try:
            filepath = self.data_dir / f"{filename}.json"
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, default=str)
            return True
        except Exception as e:
            logging.error(f"Failed to save {filename}: {e}")
            return False
    
    def load_json(self, filename: str) -> Optional[Dict[str, Any]]:
        """Load data from JSON file"""
        try:
            filepath = self.data_dir / f"{filename}.json"
            if filepath.exists():
                with open(filepath, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return None
        except Exception as e:
            logging.error(f"Failed to load {filename}: {e}")
            return None
    
    def list_files(self) -> List[str]:
        """List all data files"""
        return [f.stem for f in self.data_dir.glob("*.json")]

# =============================================================================
# FORMATTING UTILITIES
# =============================================================================

class Formatter:
    """Provides various formatting utilities for output"""
    
    @staticmethod
    def format_time(seconds: float) -> str:
        """Format time duration in a human-readable way"""
        if seconds < 1:
            return f"{seconds*1000:.0f}ms"
        elif seconds < 60:
            return f"{seconds:.2f}s"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            secs = seconds % 60
            return f"{minutes}m {secs:.0f}s"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}h {minutes}m"
    
    @staticmethod
    def format_size(bytes_size: int) -> str:
        """Format file size in human-readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if bytes_size < 1024:
                return f"{bytes_size:.1f} {unit}"
            bytes_size /= 1024
        return f"{bytes_size:.1f} TB"
    
    @staticmethod
    def format_number(number: Union[int, float], precision: int = 2) -> str:
        """Format numbers with appropriate precision and separators"""
        if isinstance(number, int) or number.is_integer():
            return f"{int(number):,}"
        else:
            return f"{number:,.{precision}f}"
    
    @staticmethod
    def create_table(headers: List[str], rows: List[List[str]], title: str = "") -> str:
        """Create a formatted table"""
        if not rows:
            return "📊 No data to display"
        
        # Calculate column widths
        col_widths = [len(header) for header in headers]
        for row in rows:
            for i, cell in enumerate(row):
                if i < len(col_widths):
                    col_widths[i] = max(col_widths[i], len(str(cell)))
        
        # Create table
        result = ""
        if title:
            result += f"\n📊 {title}\n"
            result += "=" * (sum(col_widths) + len(headers) * 3 - 1) + "\n"
        
        # Header row
        header_row = " | ".join(header.ljust(col_widths[i]) for i, header in enumerate(headers))
        result += header_row + "\n"
        result += "-" * len(header_row) + "\n"
        
        # Data rows
        for row in rows:
            data_row = " | ".join(str(cell).ljust(col_widths[i]) for i, cell in enumerate(row))
            result += data_row + "\n"
        
        return result
    
    @staticmethod
    def create_progress_bar(current: int, total: int, width: int = 30) -> str:
        """Create a text-based progress bar"""
        if total == 0:
            return "[" + "=" * width + "] 100%"
        
        progress = current / total
        filled = int(width * progress)
        bar = "=" * filled + "-" * (width - filled)
        percentage = int(progress * 100)
        
        return f"[{bar}] {percentage}% ({current}/{total})"

# =============================================================================
# VALIDATION UTILITIES
# =============================================================================

class Validator:
    """Provides validation utilities for user input and data"""
    
    @staticmethod
    def is_valid_email(email: str) -> bool:
        """Validate email format"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def is_valid_url(url: str) -> bool:
        """Validate URL format"""
        import re
        pattern = r'^https?://(?:[-\w.])+(?:\:[0-9]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$'
        return re.match(pattern, url) is not None
    
    @staticmethod
    def is_safe_filename(filename: str) -> bool:
        """Check if filename is safe for file operations"""
        import re
        # Check for dangerous patterns
        dangerous_patterns = ['..' , '/', '\\', ':', '*', '?', '"', '<', '>', '|']
        return not any(pattern in filename for pattern in dangerous_patterns)
    
    @staticmethod
    def validate_json(json_string: str) -> tuple[bool, Optional[str]]:
        """Validate JSON string"""
        try:
            json.loads(json_string)
            return True, None
        except json.JSONDecodeError as e:
            return False, str(e)
    
    @staticmethod
    def validate_number_range(value: Union[int, float], min_val: Optional[float] = None, 
                            max_val: Optional[float] = None) -> tuple[bool, Optional[str]]:
        """Validate if number is within specified range"""
        if min_val is not None and value < min_val:
            return False, f"Value {value} is below minimum {min_val}"
        if max_val is not None and value > max_val:
            return False, f"Value {value} is above maximum {max_val}"
        return True, None

# =============================================================================
# PERFORMANCE MONITORING
# =============================================================================

class PerformanceMonitor:
    """Monitors and tracks performance metrics"""
    
    def __init__(self):
        self.metrics = {}
        self.start_times = {}
    
    def start_timer(self, operation: str):
        """Start timing an operation"""
        self.start_times[operation] = time.time()
    
    def end_timer(self, operation: str) -> float:
        """End timing and return duration"""
        if operation in self.start_times:
            duration = time.time() - self.start_times[operation]
            self._record_metric(operation, duration)
            del self.start_times[operation]
            return duration
        return 0.0
    
    def _record_metric(self, operation: str, duration: float):
        """Record performance metric"""
        if operation not in self.metrics:
            self.metrics[operation] = {
                'count': 0,
                'total_time': 0,
                'min_time': float('inf'),
                'max_time': 0,
                'avg_time': 0
            }
        
        metric = self.metrics[operation]
        metric['count'] += 1
        metric['total_time'] += duration
        metric['min_time'] = min(metric['min_time'], duration)
        metric['max_time'] = max(metric['max_time'], duration)
        metric['avg_time'] = metric['total_time'] / metric['count']
    
    def get_report(self) -> str:
        """Generate performance report"""
        if not self.metrics:
            return "📊 No performance data available"
        
        report = "⚡ Performance Report:\n"
        report += "=" * 40 + "\n"
        
        for operation, metric in self.metrics.items():
            report += f"\n🔧 {operation.upper()}:\n"
            report += f"   📈 Executions: {metric['count']}\n"
            report += f"   ⏱️ Total Time: {Formatter.format_time(metric['total_time'])}\n"
            report += f"   📊 Average: {Formatter.format_time(metric['avg_time'])}\n"
            report += f"   ⚡ Fastest: {Formatter.format_time(metric['min_time'])}\n"
            report += f"   🐌 Slowest: {Formatter.format_time(metric['max_time'])}\n"
        
        return report
    
    def reset(self):
        """Reset all metrics"""
        self.metrics.clear()
        self.start_times.clear()

# =============================================================================
# ASYNC UTILITIES
# =============================================================================

class AsyncUtils:
    """Utilities for async operations"""
    
    @staticmethod
    async def run_with_timeout(coro, timeout: float):
        """Run coroutine with timeout"""
        try:
            return await asyncio.wait_for(coro, timeout=timeout)
        except asyncio.TimeoutError:
            raise TimeoutError(f"Operation timed out after {timeout} seconds")
    
    @staticmethod
    async def retry_async(coro_func, max_retries: int = 3, delay: float = 1.0):
        """Retry async function with exponential backoff"""
        for attempt in range(max_retries):
            try:
                return await coro_func()
            except Exception as e:
                if attempt == max_retries - 1:
                    raise e
                await asyncio.sleep(delay * (2 ** attempt))

# =============================================================================
# LEARNING UTILITIES
# =============================================================================

class LearningTracker:
    """Tracks learning progress and provides insights"""
    
    def __init__(self):
        self.data_manager = DataManager()
        self.session_start = datetime.now()
        self.interactions = []
    
    def log_interaction(self, agent_type: str, tool_used: str, success: bool, 
                       duration: float, topic: str = ""):
        """Log a learning interaction"""
        interaction = {
            'timestamp': datetime.now().isoformat(),
            'agent_type': agent_type,
            'tool_used': tool_used,
            'success': success,
            'duration': duration,
            'topic': topic
        }
        self.interactions.append(interaction)
    
    def get_learning_summary(self) -> str:
        """Generate learning progress summary"""
        if not self.interactions:
            return "📚 No learning interactions recorded yet"
        
        total_interactions = len(self.interactions)
        successful_interactions = sum(1 for i in self.interactions if i['success'])
        success_rate = (successful_interactions / total_interactions) * 100
        
        # Most used tools
        tool_usage = {}
        for interaction in self.interactions:
            tool = interaction['tool_used']
            tool_usage[tool] = tool_usage.get(tool, 0) + 1
        
        most_used_tool = max(tool_usage.items(), key=lambda x: x[1]) if tool_usage else ("None", 0)
        
        session_duration = datetime.now() - self.session_start
        
        summary = f"📚 Learning Progress Summary:\n"
        summary += "=" * 35 + "\n"
        summary += f"⏰ Session Duration: {Formatter.format_time(session_duration.total_seconds())}\n"
        summary += f"🔢 Total Interactions: {total_interactions}\n"
        summary += f"✅ Success Rate: {success_rate:.1f}%\n"
        summary += f"🏆 Most Used Tool: {most_used_tool[0]} ({most_used_tool[1]} times)\n"
        summary += f"🎯 Tools Explored: {len(tool_usage)}\n"
        
        return summary
    
    def save_session(self):
        """Save current session data"""
        session_data = {
            'session_start': self.session_start.isoformat(),
            'session_end': datetime.now().isoformat(),
            'interactions': self.interactions
        }
        
        filename = f"session_{self.session_start.strftime('%Y%m%d_%H%M%S')}"
        return self.data_manager.save_json(filename, session_data)

# Create global instances
data_manager = DataManager()
performance_monitor = PerformanceMonitor()
learning_tracker = LearningTracker()

# Export all utilities
__all__ = [
    'DataManager', 'Formatter', 'Validator', 'PerformanceMonitor', 
    'AsyncUtils', 'LearningTracker', 'data_manager', 'performance_monitor', 
    'learning_tracker'
]
