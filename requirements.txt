# AI Agents Practice Lab Requirements
# Install with: pip install -r requirements.txt

# Core AI Agents Framework
openai-agents>=0.1.0

# Environment and Configuration
python-dotenv>=1.0.0

# HTTP Requests
requests>=2.31.0

# Async Support
nest-asyncio>=1.5.0

# Interactive Web Interface
streamlit>=1.46.0

# Data Processing
pandas>=2.0.0
numpy>=1.24.0

# Optional: Enhanced Analytics
matplotlib>=3.7.0
plotly>=5.15.0

# Development Tools (Optional)
black>=23.0.0
flake8>=6.0.0
pytest>=7.0.0
