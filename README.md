# 🤖 Enhanced AI Agents System

A comprehensive AI agents framework with educational tools, multiple personalities, and advanced learning features.

[![GitHub Repository](https://img.shields.io/badge/GitHub-asadullah48%2Ftool-blue?logo=github)](https://github.com/asadullah48/tool.git)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![Streamlit](https://img.shields.io/badge/Streamlit-FF4B4B?logo=streamlit&logoColor=white)](https://streamlit.io/)

**Repository:** [https://github.com/asadullah48/tool.git](https://github.com/asadullah48/tool.git)
**Author:** [asadullah48](https://github.com/asadullah48)
**License:** MIT

**🎓 Learning Source:** [Panaversity - Learn Agentic AI](https://panaversity.org/)
**📚 Course Materials:** [AI Agents First](https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first)
**🌐 Website:** [panaversity.org](https://panaversity.org/)

## 🚀 Features

### 🧠 **Multiple AI Agent Personalities**
- **Helpful Assistant**: General-purpose assistance
- **AI Teacher**: Educational focus with learning strategies
- **Technical Expert**: Detailed technical explanations
- **Friendly Companion**: Casual and encouraging interactions
- **Research Assistant**: Comprehensive analysis and research
- **Personal Tutor**: Adaptive and personalized learning
- **Creative Collaborator**: Innovation and creative thinking
- **Data Analyst**: Quantitative analysis and insights

### 🛠️ **Comprehensive Tool Suite**
- **Enhanced Calculator**: Advanced mathematical operations
- **Student Database**: PIAIC student information system
- **Weather Information**: Location-based weather data
- **File Operations**: Safe file handling and management
- **Web Search Simulation**: Information retrieval
- **Data Analyzer**: Statistical analysis and insights
- **Text Processor**: Text analysis and processing
- **Learning Analytics**: Progress tracking and insights
- **System Information**: Environment and configuration data

### 📚 **Learning Features**
- **Interactive Tutorials**: Step-by-step guided learning
- **Skill Assessment**: Personalized skill evaluation
- **Progress Tracking**: Comprehensive analytics
- **Adaptive Learning Paths**: Customized learning journeys
- **Achievement System**: Milestone tracking and rewards
- **Interactive Practice Interface**: Streamlit-based hands-on practice
- **Teaching Tools**: Resources for educators and peer learning

### 🎯 **Educational Focus**
- Tool usage explanations
- Learning progress monitoring
- Performance analytics
- Interactive demonstrations
- Best practices guidance

## 📋 Prerequisites

- Python 3.8 or higher
- Gemini API key (get from [Google AI Studio](https://ai.google.dev/))
- Optional: OpenAI API key for comparison

## 🔧 Installation

### Option 1: Using UV (Recommended)
1. **Install UV** (if not already installed):
   ```bash
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

2. **Clone the repository**:
   ```bash
   git clone https://github.com/asadullah48/tool.git
   cd tool
   ```

3. **Install dependencies with UV**:
   ```bash
   uv add openai-agents python-dotenv requests nest-asyncio streamlit
   ```

4. **Configure environment**:
   - Copy `.env.template` to `.env`
   - Add your API keys to `.env`

5. **Run the application**:
   ```bash
   python main.py
   ```

6. **Run the interactive practice interface**:
   ```bash
   streamlit run practices.py
   ```

### Option 2: Using Pip
1. **Clone the repository**:
   ```bash
   git clone https://github.com/asadullah48/tool.git
   cd tool
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment**:
   - Copy `.env.template` to `.env`
   - Add your API keys to `.env`

4. **Run the application**:
   ```bash
   python main.py
   ```

5. **Run the interactive practice interface**:
   ```bash
   streamlit run practices.py
   ```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file with your configuration:

```env
# Required
GEMINI_API_KEY=your_gemini_api_key_here

# Optional
OPENAI_API_KEY=your_openai_api_key_here
DEFAULT_MODEL=gemini-2.0-flash
DEBUG=true
LOG_LEVEL=INFO
ENABLE_ANALYTICS=true
ENABLE_TUTORIAL_MODE=true
```

See `.env.template` for all available options.

## 🎮 Usage

### Interactive Practice Interface (Recommended for Learning & Teaching)

```bash
# Launch the interactive Streamlit interface
streamlit run practices.py
```

**Features:**
- 🎯 **Practice Levels**: Basic to Advanced function tool exercises
- 👥 **Teaching Mode**: Perfect for classroom or peer learning
- 📊 **Real-time Feedback**: Instant results and explanations
- 🏆 **Progress Tracking**: Visual progress indicators
- 📚 **Code Examples**: Learn by doing with interactive examples
- 🎨 **Visual Interface**: User-friendly web-based interface

### Command Line Options

```bash
# Interactive chat mode (default)
python main.py

# Run demo scenarios
python main.py --demo

# Start tutorial mode
python main.py --tutorial

# Single query mode
python main.py --query "What's the weather in Karachi?"

# Use specific agent
python main.py --agent teacher --query "Explain AI concepts"

# Show analytics
python main.py --analytics

# List available agents
python main.py --list-agents
```

### Interactive Commands

In chat mode, use these commands:

- `/help` - Show available commands
- `/agents` - List available agents
- `/switch <type>` - Switch to different agent
- `/history` - Show conversation history
- `/analytics` - Show learning analytics
- `/clear` - Clear screen
- `/quit` - Exit the application

## 📖 Examples

### Basic Usage

```python
from main import agent_manager
import asyncio

async def example():
    # Get weather information
    response = await run_single_query(
        "What's the weather in Lahore?",
        agent_type="helpful"
    )
    print(response)

asyncio.run(example())
```

### Using Different Agents

```python
# Technical explanation
technical_response = await run_single_query(
    "Explain how the calculator tool works",
    agent_type="technical"
)

# Educational approach
teacher_response = await run_single_query(
    "Teach me about data analysis",
    agent_type="teacher"
)

# Creative perspective
creative_response = await run_single_query(
    "Brainstorm creative uses for AI agents",
    agent_type="creative"
)
```

### Tool Combinations

```python
# Multi-tool query
response = await run_single_query(
    "Calculate the average of [10, 20, 30, 40, 50] and search for information about statistics",
    agent_type="analyst"
)
```

## 🎓 Learning Path

### **For Individual Learning**

### 1. **Start with Interactive Practice**
```bash
streamlit run practices.py
```
- Begin with Basic Level exercises
- Practice function tool creation
- Get instant feedback and explanations

### 2. **Command Line Tutorials**
```bash
python main.py --tutorial
# Select "basics" tutorial
```

### 3. **Explore Tools**
- Try different calculations
- Get weather information
- Search student database
- Analyze text and data

### 4. **Learn Agent Personalities**
- Switch between different agents
- Compare their responses
- Understand their specialties

### 5. **Advanced Features**
- Use learning analytics
- Complete skill assessments
- Explore file operations
- Try creative applications

### 6. **Master the System**
- Create complex queries
- Combine multiple tools
- Understand performance metrics
- Develop best practices

### **For Teaching & Classroom Use**

### 1. **Setup for Class**
```bash
# Launch the teaching interface
streamlit run practices.py
```

### 2. **Guided Learning Sessions**
- Use the practice interface for live demonstrations
- Work through exercises together
- Show real-time code execution
- Explain concepts with visual feedback

### 3. **Peer Learning Activities**
- Students can work in pairs on practice exercises
- Compare different approaches to function tools
- Share solutions and learn from each other

### 4. **Assessment & Progress**
- Track student progress through practice levels
- Use built-in analytics for assessment
- Assign specific practice exercises as homework

## 📊 Analytics and Monitoring

The system tracks:
- Tool usage frequency
- Execution times
- Success rates
- Learning progress
- Skill development
- Achievement milestones

Access analytics with:
```bash
python main.py --analytics
```

Or in chat mode:
```
/analytics
```

## 🏗️ Architecture

```
├── main.py                 # Main application entry point
├── config.py              # Configuration management
├── tools_collection.py    # Enhanced tool implementations
├── agent_personalities.py # Agent personality definitions
├── learning_features.py   # Tutorial and assessment system
├── utils.py               # Utility functions and classes
├── practices.py           # Interactive Streamlit practice interface
├── .env                   # Environment configuration
├── .env.template          # Configuration template
├── requirements.txt       # Python dependencies
├── PRACTICE_GUIDE.md      # Comprehensive practice guide
├── QUICKSTART.md          # 5-minute setup guide
├── examples/              # Example scripts and tutorials
│   ├── basic_usage.py     # Basic usage examples
│   └── advanced_examples.py # Advanced workflow examples
└── logs/                  # Application logs and analytics
    ├── agents.log         # Application logs
    └── analytics.json     # Session analytics data
```

## 🔧 Customization

### Adding New Tools

1. Create a new tool function:
```python
from agents.tool import function_tool

@function_tool("my_tool")
def my_custom_tool(parameter: str) -> str:
    """Tool description"""
    # Implementation
    return "Result"
```

2. Add to agent tools list in `main.py`

### Creating Custom Personalities

1. Add to `agent_personalities.py`:
```python
"my_personality": {
    "name": "My Custom Agent",
    "instructions": "Custom instructions...",
    "specialties": ["Custom specialty"],
    "tone": "Custom tone"
}
```

### Adding Tutorials

1. Define in `learning_features.py`:
```python
"my_tutorial": {
    "title": "My Tutorial",
    "description": "Tutorial description",
    "steps": [...]
}
```

## 🐛 Troubleshooting

### Common Issues

1. **API Key Errors**
   - Ensure your API key is correctly set in `.env`
   - Check API key permissions and quotas

2. **Import Errors**
   - Install all required dependencies
   - Check Python version compatibility

3. **Performance Issues**
   - Enable performance monitoring
   - Check network connectivity
   - Review log files for errors

### Debug Mode

Enable debug mode in `.env`:
```env
DEBUG=true
VERBOSE_LOGGING=true
```

## 📝 Contributing

We welcome contributions to improve the Enhanced AI Agents System! Here's how you can help:

### **How to Contribute:**
1. **Fork the repository**: [https://github.com/asadullah48/tool.git](https://github.com/asadullah48/tool.git)
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes**: Add new features, fix bugs, or improve documentation
4. **Add tests**: Ensure your changes work correctly
5. **Commit your changes**: `git commit -m 'Add amazing feature'`
6. **Push to the branch**: `git push origin feature/amazing-feature`
7. **Submit a pull request**: Open a PR with a clear description

### **Areas for Contribution:**
- 🛠️ New function tools and exercises
- 🎨 UI/UX improvements for the practice interface
- 📚 Additional tutorials and learning materials
- 🐛 Bug fixes and performance improvements
- 📖 Documentation enhancements
- 🌍 Internationalization and translations

### **Development Setup:**
```bash
git clone https://github.com/asadullah48/tool.git
cd tool
uv add --dev pytest black flake8
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

### **MIT License Summary:**
- ✅ Commercial use allowed
- ✅ Modification allowed
- ✅ Distribution allowed
- ✅ Private use allowed
- ❗ License and copyright notice required

## 🙏 Acknowledgments

### **Primary Learning Source:**
- **[Panaversity](https://panaversity.org/)** - Learn Agentic AI course and educational methodology
- **[AI Agents First Course](https://github.com/panaversity/learn-agentic-ai/tree/main/01_ai_agents_first)** - Core concepts and implementation patterns
- **Panaversity Educational Team** - Innovative teaching approaches and curriculum design

### **Technical Foundations:**
- **OpenAI Agents Framework** - Core AI agent functionality
- **Google Gemini API** - Powerful language model integration
- **Streamlit** - Interactive web interface framework
- **Educational AI Research Community** - Inspiration and best practices

### **Community & Development:**
- **[asadullah48](https://github.com/asadullah48)** - Project creator and maintainer
- **Panaversity Students** - Collaborative learning and feedback
- **Open Source Community** - Tools and libraries that make this possible
- **Contributors** - Everyone who helps improve this project

### **Educational Impact:**
This project is built upon the solid educational foundation provided by Panaversity's Learn Agentic AI course. The teaching methodologies, progressive learning approach, and practical implementation patterns are directly inspired by Panaversity's innovative educational framework.

## 📞 Support

### **Getting Help:**
- 📖 **Documentation**: Check [README.md](README.md) and [PRACTICE_GUIDE.md](PRACTICE_GUIDE.md)
- 🚀 **Quick Start**: Follow the [QUICKSTART.md](QUICKSTART.md) guide
- 💻 **Examples**: Review scripts in the [examples/](examples/) directory
- 🎓 **Tutorials**: Use the built-in tutorial system (`python main.py --tutorial`)
- 🐛 **Debugging**: Enable debug mode in `.env` for detailed logs

### **Community Support:**
- 🐛 **Issues**: Report bugs on [GitHub Issues](https://github.com/asadullah48/tool/issues)
- 💡 **Feature Requests**: Suggest improvements via GitHub Issues
- 🤝 **Discussions**: Join conversations on [GitHub Discussions](https://github.com/asadullah48/tool/discussions)
- 📧 **Contact**: Reach out to [asadullah48](https://github.com/asadullah48)

### **Resources:**
- 📚 **Practice Guide**: [PRACTICE_GUIDE.md](PRACTICE_GUIDE.md)
- ⚡ **Quick Start**: [QUICKSTART.md](QUICKSTART.md)
- 🔧 **Examples**: [examples/basic_usage.py](examples/basic_usage.py)
- 🎯 **Advanced Examples**: [examples/advanced_examples.py](examples/advanced_examples.py)

---

## 🎓 Interactive Practice Interface

### **Perfect for Learning & Teaching**

The Streamlit-based practice interface provides an interactive environment for mastering function tools:

#### **🎯 Three Learning Modes:**

1. **🎓 Learning Mode** - Individual practice with progressive exercises
2. **👩‍🏫 Teaching Mode** - Classroom demonstrations and peer learning
3. **📊 Analytics Dashboard** - Progress tracking and performance metrics

#### **📚 Four Skill Levels:**

- **🟢 Basic** - Function tool fundamentals (3 exercises)
- **🟡 Intermediate** - Enhanced tools with validation (3 exercises)
- **🟠 Advanced** - Complex tools with integrations (3 exercises)
- **🔴 Expert** - AI integration and workflows (3 exercises)

#### **🚀 Getting Started:**

```bash
# Launch the practice interface
streamlit run practices.py
```

#### **✨ Key Features:**

- **Interactive Code Editor** - Write and test code in real-time
- **Instant Feedback** - Get immediate results and explanations
- **Progressive Learning** - Start basic, advance to expert level
- **Teaching Tools** - Lesson plans, live coding, assignments
- **Progress Tracking** - Visual analytics and achievement system
- **Peer Learning** - Perfect for classroom and study groups

#### **📖 Comprehensive Guides:**

- **[Practice Guide](PRACTICE_GUIDE.md)** - Complete learning and teaching guide
- **[Quick Start](QUICKSTART.md)** - 5-minute setup guide
- **[Examples](examples/)** - Code examples and tutorials

---

## 🚀 **Get Started Now!**

### **1. Clone the Repository:**
```bash
git clone https://github.com/asadullah48/tool.git
cd tool
```

### **2. Install Dependencies:**
```bash
uv add streamlit  # or pip install -r requirements.txt
```

### **3. Launch Practice Interface:**
```bash
streamlit run practices.py
```

### **4. Start Learning:**
Choose "🎓 Learning Mode" → "Basic Level" → Begin your journey!

---

**Ready to master AI agent function tools? Start with the interactive practice interface! 🚀**

**Repository:** [https://github.com/asadullah48/tool.git](https://github.com/asadullah48/tool.git)
**Happy Learning with AI Agents! 🚀🤖**